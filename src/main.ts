import '@/styles/main.css';

import { Game } from '@/core/Game';
import { GameConfig } from '@/types';

// Environment-based configuration
const createGameConfig = (): GameConfig => ({
    debug: import.meta.env['GAME_DEBUG'] === 'true',
    performanceMonitor: import.meta.env['GAME_PERFORMANCE_MONITOR'] === 'true',
    targetFPS: Number(import.meta.env['GAME_TARGET_FPS']) || 60,
    maxBullets: Number(import.meta.env['GAME_MAX_BULLETS']) || 1000,
    memoryWarningThreshold: Number(import.meta.env['GAME_MEMORY_WARNING_THRESHOLD']) || 100,
    memoryCriticalThreshold: Number(import.meta.env['GAME_MEMORY_CRITICAL_THRESHOLD']) || 200,
});

// Enterprise-grade error handling
const handleError = (error: Error): void => {
    console.error('Game initialization failed:', error);

    if (import.meta.env['GAME_ERROR_REPORTING'] === 'true') {
        // TODO: Send to error reporting service
    }

    // Show user-friendly error message
    const errorElement = document.createElement('div');
    errorElement.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #ff4444;
      color: white;
      padding: 20px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      text-align: center;
      z-index: 9999;
    ">
      <h3>Game Failed to Load</h3>
      <p>Please refresh the page and try again.</p>
      <button onclick="location.reload()" style="
        background: white;
        color: #ff4444;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
      ">Reload Game</button>
    </div>
  `;
    document.body.appendChild(errorElement);
};

// Application initialization
const initializeGame = async (): Promise<void> => {
    try {
        const config = createGameConfig();
        const canvas = document.getElementById('game-canvas') as HTMLCanvasElement;

        if (!canvas) {
            throw new Error('Game canvas element not found');
        }

        const game = new Game(canvas, config);
        await game.initialize();

        // Start the game loop
        game.start();

        // Development tools
        if (config.debug) {
            // Expose game instance for debugging
            (window as any).game = game;
            console.log('Game initialized in debug mode');
        }
    } catch (error) {
        handleError(error as Error);
    }
};

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeGame);
} else {
    void initializeGame();
}
