/**
 * StateManager - Manages game states and state transitions
 * Implements state machine pattern with stack-based state management
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { 
    IStateManager, 
    IGameState, 
    IGameEntity, 
    InputEvent 
} from '@/types/CoreInterfaces.types';

// Game state implementations
export class MenuState implements IGameState {
    public readonly name = 'menu';

    public enter(): void {
        console.log('Entering menu state');
        // Show main menu UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('main-menu');
        }
    }

    public update(deltaTime: number): void {
        // Menu state update logic
        // Handle menu animations, etc.
    }

    public exit(): void {
        console.log('Exiting menu state');
        // Hide menu UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('main-menu');
        }
    }

    public handleInput(event: InputEvent): void {
        // Handle menu navigation input
        if (event.type === 'keydown' && event.key === 'Enter') {
            // Start game when Enter is pressed
            const stateManager = (window as any).game?.stateManager;
            if (stateManager) {
                stateManager.changeState('playing');
            }
        }
    }
}

export class PlayingState implements IGameState {
    public readonly name = 'playing';

    public enter(): void {
        console.log('Entering playing state');
        // Show game HUD
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('hud');
        }
    }

    public update(deltaTime: number): void {
        // Game playing state update logic
        // This is where the main game loop happens
    }

    public exit(): void {
        console.log('Exiting playing state');
        // Hide game HUD
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('hud');
        }
    }

    public handleInput(event: InputEvent): void {
        // Handle game input
        if (event.type === 'keydown' && event.key === 'Escape') {
            // Pause game when Escape is pressed
            const stateManager = (window as any).game?.stateManager;
            if (stateManager) {
                stateManager.pushState('paused');
            }
        }
    }
}

export class PausedState implements IGameState {
    public readonly name = 'paused';

    public enter(): void {
        console.log('Entering paused state');
        // Show pause menu
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('pause-menu');
        }
        
        // Pause the game
        const game = (window as any).game;
        if (game) {
            game.pause();
        }
    }

    public update(deltaTime: number): void {
        // Paused state update logic (minimal)
        // Game is paused, so most updates should be suspended
    }

    public exit(): void {
        console.log('Exiting paused state');
        // Hide pause menu
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('pause-menu');
        }
        
        // Resume the game
        const game = (window as any).game;
        if (game) {
            game.resume();
        }
    }

    public handleInput(event: InputEvent): void {
        // Handle pause menu input
        if (event.type === 'keydown' && event.key === 'Escape') {
            // Resume game when Escape is pressed again
            const stateManager = (window as any).game?.stateManager;
            if (stateManager) {
                stateManager.popState();
            }
        }
    }
}

export class GameOverState implements IGameState {
    public readonly name = 'gameOver';

    public enter(): void {
        console.log('Entering game over state');
        // Show game over UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('game-over');
        }
    }

    public update(deltaTime: number): void {
        // Game over state update logic
        // Handle score submission, animations, etc.
    }

    public exit(): void {
        console.log('Exiting game over state');
        // Hide game over UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('game-over');
        }
    }

    public handleInput(event: InputEvent): void {
        // Handle game over input
        if (event.type === 'keydown' && event.key === 'Enter') {
            // Return to menu when Enter is pressed
            const stateManager = (window as any).game?.stateManager;
            if (stateManager) {
                stateManager.changeState('menu');
            }
        }
    }
}

export class VictoryState implements IGameState {
    public readonly name = 'victory';

    public enter(): void {
        console.log('Entering victory state');
        // Show victory UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('victory');
        }
    }

    public update(deltaTime: number): void {
        // Victory state update logic
        // Handle celebrations, score display, etc.
    }

    public exit(): void {
        console.log('Exiting victory state');
        // Hide victory UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('victory');
        }
    }

    public handleInput(event: InputEvent): void {
        // Handle victory input
        if (event.type === 'keydown' && event.key === 'Enter') {
            // Return to menu when Enter is pressed
            const stateManager = (window as any).game?.stateManager;
            if (stateManager) {
                stateManager.changeState('menu');
            }
        }
    }
}

export class LoadingState implements IGameState {
    public readonly name = 'loading';

    public enter(): void {
        console.log('Entering loading state');
        // Show loading UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.showMenu('loading');
        }
    }

    public update(deltaTime: number): void {
        // Loading state update logic
        // Update loading progress, etc.
    }

    public exit(): void {
        console.log('Exiting loading state');
        // Hide loading UI
        const uiManager = (window as any).game?.uiManager;
        if (uiManager) {
            uiManager.hideMenu('loading');
        }
    }

    public handleInput(event: InputEvent): void {
        // Loading state typically doesn't handle input
        // Could handle cancel loading if needed
    }
}

export class StateManager extends BaseSystem implements IStateManager {
    public static readonly NAME = 'StateManager';
    public static readonly PRIORITY = -1; // Very high priority, before other systems

    private states: Map<string, IGameState> = new Map();
    private stateStack: IGameState[] = [];
    private currentState: IGameState | null = null;
    private pendingStateChange: string | null = null;

    constructor() {
        super(StateManager.NAME, StateManager.PRIORITY);
        this.initializeDefaultStates();
    }

    /**
     * Initialize the state manager
     */
    public override async initialize(): Promise<void> {
        console.log('StateManager initialized');
    }

    /**
     * Initialize default game states
     */
    private initializeDefaultStates(): void {
        this.addState(new MenuState());
        this.addState(new PlayingState());
        this.addState(new PausedState());
        this.addState(new GameOverState());
        this.addState(new VictoryState());
        this.addState(new LoadingState());
    }

    /**
     * Update state manager - process current state
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        // Handle pending state changes
        if (this.pendingStateChange) {
            this.performStateChange(this.pendingStateChange);
            this.pendingStateChange = null;
        }

        // Update current state
        if (this.currentState) {
            this.currentState.update(deltaTime);
        }
    }

    /**
     * Add a state to the state manager
     */
    public addState(state: IGameState): void {
        this.states.set(state.name, state);
    }

    /**
     * Change to a new state (clears state stack)
     */
    public changeState(stateName: string): void {
        this.pendingStateChange = stateName;
    }

    /**
     * Push a state onto the stack (keeps current state)
     */
    public pushState(stateName: string): void {
        const state = this.states.get(stateName);
        if (!state) {
            console.error(`State '${stateName}' not found`);
            return;
        }

        // Push current state to stack if it exists
        if (this.currentState) {
            this.stateStack.push(this.currentState);
        }

        // Enter new state
        this.currentState = state;
        this.currentState.enter();
    }

    /**
     * Pop the current state and return to previous
     */
    public popState(): void {
        if (this.currentState) {
            this.currentState.exit();
        }

        // Pop previous state from stack
        if (this.stateStack.length > 0) {
            this.currentState = this.stateStack.pop()!;
            // Note: We don't call enter() again since we're returning to a previous state
        } else {
            this.currentState = null;
        }
    }

    /**
     * Get current active state
     */
    public getCurrentState(): IGameState | null {
        return this.currentState;
    }

    /**
     * Get current state name
     */
    public getCurrentStateName(): string {
        return this.currentState?.name || 'none';
    }

    /**
     * Check if in specific state
     */
    public isInState(stateName: string): boolean {
        return this.currentState?.name === stateName;
    }

    /**
     * Handle input events for current state
     */
    public handleInput(event: InputEvent): void {
        if (this.currentState && this.currentState.handleInput) {
            this.currentState.handleInput(event);
        }
    }

    /**
     * Perform actual state change
     */
    private performStateChange(stateName: string): void {
        const newState = this.states.get(stateName);
        if (!newState) {
            console.error(`State '${stateName}' not found`);
            return;
        }

        // Exit current state
        if (this.currentState) {
            this.currentState.exit();
        }

        // Clear state stack on state change
        this.stateStack.length = 0;

        // Enter new state
        this.currentState = newState;
        this.currentState.enter();

        console.log(`State changed to: ${stateName}`);
    }

    /**
     * Get all available state names
     */
    public getAvailableStates(): string[] {
        return Array.from(this.states.keys());
    }

    /**
     * Initialize with a specific state
     */
    public async init(initialState: string = 'loading'): Promise<void> {
        await this.initialize();
        this.changeState(initialState);
    }

    /**
     * Cleanup state manager
     */
    public override cleanup(): void {
        // Exit current state
        if (this.currentState) {
            this.currentState.exit();
        }

        // Clear state stack and current state
        this.stateStack.length = 0;
        this.currentState = null;
        this.pendingStateChange = null;

        console.log('StateManager cleaned up');
    }
}
