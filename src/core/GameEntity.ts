/**
 * GameEntity - Core game entity implementation
 * Implements the IGameEntity interface for the Entity Component System
 */

import type { IGameEntity, IComponent, Vector2 } from '@/types';

export class GameEntity implements IGameEntity {
    public readonly id: string;
    public position: Vector2;
    public rotation: number = 0;
    public scale: Vector2;
    public active: boolean = true;
    public readonly components: Map<string, IComponent> = new Map();
    
    private _type: string = '';
    
    constructor(id: string, position: Vector2 = { x: 0, y: 0 }) {
        this.id = id;
        this.position = { ...position };
        this.scale = { x: 1, y: 1 };
    }
    
    /**
     * Add a component to this entity
     */
    public addComponent<T extends IComponent>(component: T): void {
        if (this.components.has(component.type)) {
            console.warn(`Entity ${this.id} already has component of type ${component.type}`);
            return;
        }
        
        this.components.set(component.type, component);
    }
    
    /**
     * Remove a component by type name
     */
    public removeComponent(type: string): void {
        const component = this.components.get(type);
        if (component) {
            // Call destroy if component has cleanup logic
            if (component.destroy) {
                component.destroy();
            }
            this.components.delete(type);
        }
    }
    
    /**
     * Get a component by type name
     */
    public getComponent<T extends IComponent>(type: string): T | null {
        return (this.components.get(type) as T) || null;
    }
    
    /**
     * Check if entity has a specific component type
     */
    public hasComponent(type: string): boolean {
        return this.components.has(type);
    }
    
    /**
     * Get all component types for this entity
     */
    public getComponentTypes(): string[] {
        return Array.from(this.components.keys());
    }
    
    /**
     * Set the entity type (used for categorization)
     */
    public setType(type: string): void {
        this._type = type;
    }
    
    /**
     * Get the entity type
     */
    public getType(): string {
        return this._type;
    }
    
    /**
     * Update all components on this entity
     */
    public update(deltaTime: number): void {
        if (!this.active) {
            return;
        }
        
        for (const component of this.components.values()) {
            if (component.enabled) {
                try {
                    component.update(deltaTime);
                } catch (error) {
                    // eslint-disable-next-line no-console
                    console.error(`Error updating component ${component.type} on entity ${this.id}:`, error);
                }
            }
        }
    }
    
    /**
     * Reset entity state for object pooling
     */
    public reset(id: string, position: Vector2): void {
        // Clear existing components
        for (const component of this.components.values()) {
            if (component.destroy) {
                component.destroy();
            }
        }
        this.components.clear();
        
        // Reset properties
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (this as any).id = id; // Cast to any to bypass readonly
        this.position = { ...position };
        this.rotation = 0;
        this.scale = { x: 1, y: 1 };
        this.active = true;
        this._type = '';
    }
    
    /**
     * Cleanup and destroy the entity
     */
    public destroy(): void {
        // Destroy all components
        for (const component of this.components.values()) {
            if (component.destroy) {
                component.destroy();
            }
        }
        this.components.clear();
        this.active = false;
    }
    
    /**
     * Clone this entity (useful for spawning similar entities)
     */
    public clone(newId: string): GameEntity {
        const clone = new GameEntity(newId, this.position);
        clone.rotation = this.rotation;
        clone.scale = { ...this.scale };
        clone.setType(this._type);
        
        // Note: Components are not cloned as they may have unique state
        // Components should be added separately to the cloned entity
        
        return clone;
    }
    
    /**
     * Serialize entity data for debugging or save/load
     */
    public serialize(): Record<string, unknown> {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const componentData: Record<string, any> = {};
        
        for (const [type, component] of this.components) {
            if (component.serialize) {
                componentData[type] = component.serialize();
            }
        }
        
        return {
            id: this.id,
            type: this._type,
            position: this.position,
            rotation: this.rotation,
            scale: this.scale,
            active: this.active,
            components: componentData
        };
    }
    
    /**
     * Get distance to another entity
     */
    public distanceTo(other: IGameEntity): number {
        const dx = this.position.x - other.position.x;
        const dy = this.position.y - other.position.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Get squared distance to another entity (more efficient for comparisons)
     */
    public distanceSquaredTo(other: IGameEntity): number {
        const dx = this.position.x - other.position.x;
        const dy = this.position.y - other.position.y;
        return dx * dx + dy * dy;
    }
    
    /**
     * Check if this entity is within a certain distance of another entity
     */
    public isNear(other: IGameEntity, distance: number): boolean {
        return this.distanceSquaredTo(other) <= distance * distance;
    }
}
