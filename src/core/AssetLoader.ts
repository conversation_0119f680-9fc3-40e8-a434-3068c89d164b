/**
 * AssetLoader - Handles loading and caching of game assets
 * Supports textures, audio, 3D models, JSON data, and shaders
 */

import * as THREE from 'three';
import type { IAssetLoader } from '@/types/CoreInterfaces.types';
import { AssetType } from '@/types/CoreInterfaces.types';

interface AssetConfig {
    id: string;
    url: string;
    type: AssetType;
    preload?: boolean;
    cache?: boolean;
}

interface LoadingProgress {
    loaded: number;
    total: number;
    percentage: number;
}

interface AssetManifest {
    textures: Record<string, string>;
    models: Record<string, string>;
    audio: Record<string, string>;
    shaders: Record<string, string>;
    json: Record<string, string>;
}

export class AssetLoader implements IAssetLoader {
    private assets: Map<string, any> = new Map();
    private loadingPromises: Map<string, Promise<any>> = new Map();
    private loadProgress: Map<string, number> = new Map();
    private manifest: AssetManifest | null = null;
    
    // Three.js loaders
    private textureLoader: THREE.TextureLoader;
    private audioLoader: THREE.AudioLoader;
    private fileLoader: THREE.FileLoader;
    
    // Loading statistics
    private totalAssets: number = 0;
    private loadedAssets: number = 0;

    constructor() {
        this.textureLoader = new THREE.TextureLoader();
        this.audioLoader = new THREE.AudioLoader();
        this.fileLoader = new THREE.FileLoader();
        
        // Set up loading manager for progress tracking
        const loadingManager = new THREE.LoadingManager();
        loadingManager.onProgress = (url, loaded, total) => {
            this.updateProgress(url, loaded, total);
        };
        
        this.textureLoader.manager = loadingManager;
        this.audioLoader.manager = loadingManager;
        this.fileLoader.manager = loadingManager;
    }

    /**
     * Initialize the asset loader
     */
    public async init(): Promise<void> {
        console.log('AssetLoader initialized');
        
        // Try to load asset manifest
        try {
            await this.loadManifest();
        } catch (error) {
            console.warn('Could not load asset manifest:', error);
        }
    }

    /**
     * Load asset manifest from file
     */
    private async loadManifest(): Promise<void> {
        try {
            const response = await fetch('/assets/manifest.json');
            this.manifest = await response.json();
            console.log('Asset manifest loaded:', this.manifest);
        } catch (error) {
            console.warn('Asset manifest not found, using default paths');
            this.manifest = {
                textures: {},
                models: {},
                audio: {},
                shaders: {},
                json: {}
            };
        }
    }

    /**
     * Load a single asset
     */
    public async load<T>(url: string, type: AssetType): Promise<T> {
        // Check if already loaded
        if (this.assets.has(url)) {
            return this.assets.get(url) as T;
        }

        // Check if currently loading
        if (this.loadingPromises.has(url)) {
            return this.loadingPromises.get(url) as Promise<T>;
        }

        // Start loading
        const loadPromise = this.loadAssetByType(url, type);
        this.loadingPromises.set(url, loadPromise);

        try {
            const asset = await loadPromise;
            this.assets.set(url, asset);
            this.loadingPromises.delete(url);
            this.loadProgress.set(url, 100);
            this.loadedAssets++;
            
            console.log(`Asset loaded: ${url}`);
            return asset as T;
        } catch (error) {
            this.loadingPromises.delete(url);
            console.error(`Failed to load asset: ${url}`, error);
            throw error;
        }
    }

    /**
     * Load multiple assets
     */
    public async loadMultiple(configs: AssetConfig[]): Promise<void> {
        this.totalAssets = configs.length;
        this.loadedAssets = 0;

        const loadPromises = configs.map(config => 
            this.load(config.url, config.type).catch(error => {
                console.error(`Failed to load ${config.id}:`, error);
                return null;
            })
        );

        await Promise.allSettled(loadPromises);
        console.log(`Loaded ${this.loadedAssets}/${this.totalAssets} assets`);
    }

    /**
     * Load assets by type using appropriate loader
     */
    private async loadAssetByType(url: string, type: AssetType): Promise<any> {
        switch (type) {
        case AssetType.TEXTURE:
            return this.loadTexture(url);
        case AssetType.AUDIO:
            return this.loadAudio(url);
        case AssetType.JSON:
            return this.loadJSON(url);
        case AssetType.MODEL:
            return this.loadModel(url);
        case AssetType.SHADER:
            return this.loadShader(url);
        default:
            throw new Error(`Unsupported asset type: ${type}`);
        }
    }

    /**
     * Load texture asset
     */
    private async loadTexture(url: string): Promise<THREE.Texture> {
        return new Promise((resolve, reject) => {
            this.textureLoader.load(
                url,
                (texture) => {
                    // Set texture properties for pixel art games
                    texture.magFilter = THREE.NearestFilter;
                    texture.minFilter = THREE.NearestFilter;
                    texture.wrapS = THREE.ClampToEdgeWrapping;
                    texture.wrapT = THREE.ClampToEdgeWrapping;
                    resolve(texture);
                },
                (progress) => {
                    // Progress callback
                    const percentage = (progress.loaded / progress.total) * 100;
                    this.loadProgress.set(url, percentage);
                },
                reject
            );
        });
    }

    /**
     * Load audio asset
     */
    private async loadAudio(url: string): Promise<AudioBuffer> {
        return new Promise((resolve, reject) => {
            this.audioLoader.load(
                url,
                resolve,
                (progress) => {
                    const percentage = (progress.loaded / progress.total) * 100;
                    this.loadProgress.set(url, percentage);
                },
                reject
            );
        });
    }

    /**
     * Load JSON data
     */
    private async loadJSON(url: string): Promise<any> {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to load JSON: ${response.statusText}`);
        }
        return response.json();
    }

    /**
     * Load 3D model (basic implementation - would need GLTFLoader for full models)
     */
    private async loadModel(url: string): Promise<any> {
        // For now, return a placeholder
        // In a full implementation, you'd use GLTFLoader or other model loaders
        console.warn('Model loading not yet implemented:', url);
        return Promise.resolve(null);
    }

    /**
     * Load shader files
     */
    private async loadShader(url: string): Promise<string> {
        return new Promise((resolve, reject) => {
            this.fileLoader.load(
                url,
                (data) => {
                    // Ensure we return a string
                    resolve(typeof data === 'string' ? data : data.toString());
                },
                (progress) => {
                    const percentage = (progress.loaded / progress.total) * 100;
                    this.loadProgress.set(url, percentage);
                },
                reject
            );
        });
    }

    /**
     * Check if asset is loaded
     */
    public isLoaded(url: string): boolean {
        return this.assets.has(url);
    }

    /**
     * Get loaded asset
     */
    public get<T>(url: string): T | null {
        return this.assets.get(url) || null;
    }

    /**
     * Get asset by ID from manifest
     */
    public getById<T>(id: string, type: AssetType): T | null {
        if (!this.manifest) {return null;}

        let url: string | undefined;
        switch (type) {
        case AssetType.TEXTURE:
            url = this.manifest.textures[id];
            break;
        case AssetType.AUDIO:
            url = this.manifest.audio[id];
            break;
        case AssetType.MODEL:
            url = this.manifest.models[id];
            break;
        case AssetType.SHADER:
            url = this.manifest.shaders[id];
            break;
        case AssetType.JSON:
            url = this.manifest.json[id];
            break;
        }

        return url ? this.get<T>(url) : null;
    }

    /**
     * Unload an asset
     */
    public unload(url: string): void {
        const asset = this.assets.get(url);
        if (asset) {
            // Dispose of Three.js resources if applicable
            if (asset instanceof THREE.Texture) {
                asset.dispose();
            }
            
            this.assets.delete(url);
            this.loadProgress.delete(url);
            console.log(`Asset unloaded: ${url}`);
        }
    }

    /**
     * Unload all assets
     */
    public unloadAll(): void {
        for (const [url, asset] of this.assets) {
            if (asset instanceof THREE.Texture) {
                asset.dispose();
            }
        }
        
        this.assets.clear();
        this.loadProgress.clear();
        this.loadingPromises.clear();
        this.loadedAssets = 0;
        this.totalAssets = 0;
        
        console.log('All assets unloaded');
    }

    /**
     * Get loading progress for a specific asset
     */
    public getProgress(url: string): number {
        return this.loadProgress.get(url) || 0;
    }

    /**
     * Get overall loading progress
     */
    public getOverallProgress(): LoadingProgress {
        const loaded = this.loadedAssets;
        const total = this.totalAssets;
        const percentage = total > 0 ? (loaded / total) * 100 : 0;
        
        return { loaded, total, percentage };
    }

    /**
     * Update progress tracking
     */
    private updateProgress(url: string, loaded: number, total: number): void {
        const percentage = total > 0 ? (loaded / total) * 100 : 0;
        this.loadProgress.set(url, percentage);
    }

    /**
     * Preload essential assets
     */
    public async preloadEssentials(): Promise<void> {
        const essentialAssets: AssetConfig[] = [
            // Add essential assets here
            { id: 'player_texture', url: '/assets/textures/player.png', type: AssetType.TEXTURE },
            { id: 'bullet_texture', url: '/assets/textures/bullet.png', type: AssetType.TEXTURE },
            { id: 'enemy_texture', url: '/assets/textures/enemy.png', type: AssetType.TEXTURE },
            // Audio
            { id: 'shoot_sound', url: '/assets/audio/shoot.ogg', type: AssetType.AUDIO },
            { id: 'hit_sound', url: '/assets/audio/hit.ogg', type: AssetType.AUDIO },
        ];

        console.log('Preloading essential assets...');
        await this.loadMultiple(essentialAssets);
        console.log('Essential assets preloaded');
    }

    /**
     * Get asset statistics
     */
    public getStats(): { 
        totalLoaded: number;
        totalSize: number;
        cacheHitRate: number;
        } {
        return {
            totalLoaded: this.assets.size,
            totalSize: 0, // Would need to track actual file sizes
            cacheHitRate: 0 // Would need to track cache hits vs misses
        };
    }

    /**
     * Clear cache and free memory
     */
    public clearCache(): void {
        this.unloadAll();
        
        // Force garbage collection if available
        if ((window as any).gc) {
            (window as any).gc();
        }
    }

    /**
     * Cleanup asset loader
     */
    public destroy(): void {
        this.unloadAll();
        console.log('AssetLoader destroyed');
    }
}
