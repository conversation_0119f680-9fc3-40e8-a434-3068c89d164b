/**
 * Entity Manager - manages all game entities and their lifecycle
 * Implements efficient ECS pattern with object pooling for performance
 */

import type { IGameEntity, Vector2 } from '@/types';
import { GameEntity } from './GameEntity';

export class EntityManager {
    private entities: Map<string, IGameEntity> = new Map();
    private entitiesByType: Map<string, Set<IGameEntity>> = new Map();
    private entityPool: GameEntity[] = [];
    private nextEntityId: number = 1;
    private maxPoolSize: number = 1000;
    
    constructor() {
        // Pre-allocate some entities for performance
        this.preAllocateEntities(100);
    }
    
    /**
     * Initialize the entity manager
     */
    public async init(): Promise<void> {
        // eslint-disable-next-line no-console
        console.log('EntityManager initialized');
    }
    
    /**
     * Pre-allocate entities for object pooling
     */
    private preAllocateEntities(count: number): void {
        for (let i = 0; i < count; i++) {
            const entity = new GameEntity(`pool_${i}`, 'pooled', { x: 0, y: 0 });
            entity.active = false;
            this.entityPool.push(entity);
        }
    }
    
    /**
     * Create a new entity
     */
    public createEntity(type: string, position?: Vector2): IGameEntity {
        let entity: GameEntity;

        // Try to get from pool first
        if (this.entityPool.length > 0) {
            entity = this.entityPool.pop()!;
            entity.reset(`entity_${this.nextEntityId++}`, type, position || { x: 0, y: 0 });
        } else {
            entity = new GameEntity(`entity_${this.nextEntityId++}`, type, position || { x: 0, y: 0 });
        }

        entity.active = true;

        // Add to tracking maps
        this.entities.set(entity.id, entity);

        if (!this.entitiesByType.has(type)) {
            this.entitiesByType.set(type, new Set());
        }
        this.entitiesByType.get(type)!.add(entity);

        return entity;
    }

    /**
     * Add an existing entity to the manager (alias for addEntity expected by systems)
     */
    public addEntity(entity: IGameEntity): void {
        if (this.entities.has(entity.id)) {
            console.warn(`Entity ${entity.id} already exists in manager`);
            return;
        }

        // Add to tracking maps
        this.entities.set(entity.id, entity);

        const type = entity.getType();
        if (!this.entitiesByType.has(type)) {
            this.entitiesByType.set(type, new Set());
        }
        this.entitiesByType.get(type)!.add(entity);
    }
    
    /**
     * Destroy an entity
     */
    public destroyEntity(entityId: string): void {
        const entity = this.entities.get(entityId);
        if (!entity) {
            return;
        }
        
        // Remove from type tracking
        const type = entity.getType();
        if (type) {
            const typeSet = this.entitiesByType.get(type);
            if (typeSet) {
                typeSet.delete(entity);
                if (typeSet.size === 0) {
                    this.entitiesByType.delete(type);
                }
            }
        }
        
        // Clean up entity
        entity.destroy();
        
        // Remove from main tracking
        this.entities.delete(entityId);
        
        // Return to pool if there's space
        if (this.entityPool.length < this.maxPoolSize) {
            entity.active = false;
            this.entityPool.push(entity as GameEntity);
        }
    }
    
    /**
     * Get an entity by ID
     */
    public getEntity(entityId: string): IGameEntity | null {
        return this.entities.get(entityId) || null;
    }
    
    /**
     * Get all entities
     */
    public getAllEntities(): IGameEntity[] {
        return Array.from(this.entities.values()).filter(entity => entity.active);
    }
    
    /**
     * Get entities by type
     */
    public getEntitiesByType(type: string): IGameEntity[] {
        const typeSet = this.entitiesByType.get(type);
        return typeSet ? Array.from(typeSet).filter(entity => entity.active) : [];
    }
    
    /**
     * Get entities with specific components
     */
    public getEntitiesWithComponents(componentTypes: string[]): IGameEntity[] {
        return this.getAllEntities().filter(entity => 
            componentTypes.every(type => entity.hasComponent(type))
        );
    }
    
    /**
     * Get entities in a radius around a position
     */
    public getEntitiesInRadius(position: Vector2, radius: number, type?: string): IGameEntity[] {
        const entities = type ? this.getEntitiesByType(type) : this.getAllEntities();
        
        return entities.filter(entity => {
            const dx = entity.position.x - position.x;
            const dy = entity.position.y - position.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            return distance <= radius;
        });
    }
    
    /**
     * Update all entities (called by systems)
     */
    public update(_deltaTime: number): void {
        // This is primarily handled by systems, but we can do cleanup here
        this.cleanupInactiveEntities();
    }
    
    /**
     * Clean up inactive entities
     */
    private cleanupInactiveEntities(): void {
        const inactiveEntities: string[] = [];
        
        this.entities.forEach((entity, id) => {
            if (!entity.active) {
                inactiveEntities.push(id);
            }
        });
        
        inactiveEntities.forEach(id => this.destroyEntity(id));
    }
    
    /**
     * Get entity count
     */
    public getEntityCount(): number {
        return this.entities.size;
    }
    
    /**
     * Get pool statistics
     */
    public getPoolStats(): { poolSize: number; activeEntities: number; totalCreated: number } {
        return {
            poolSize: this.entityPool.length,
            activeEntities: this.entities.size,
            totalCreated: this.nextEntityId - 1
        };
    }
    
    /**
     * Clear all entities
     */
    public clear(): void {
        // Destroy all entities
        const entityIds = Array.from(this.entities.keys());
        entityIds.forEach(id => this.destroyEntity(id));
        
        // Clear tracking maps
        this.entities.clear();
        this.entitiesByType.clear();
    }
    
    /**
     * Destroy the entity manager
     */
    public destroy(): void {
        this.clear();
        this.entityPool.length = 0;
        // eslint-disable-next-line no-console
        console.log('EntityManager destroyed');
    }
}
