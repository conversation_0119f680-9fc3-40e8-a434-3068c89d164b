/**
 * BaseSystem - Abstract base class for all game systems
 * Provides common functionality and enforces the ISystem interface
 */

import type { ISystem, IGameEntity, SystemStatus } from '@/types';

export abstract class BaseSystem implements ISystem {
    public readonly name: string;
    public readonly priority: number;
    public enabled: boolean = true;
    
    protected requiredComponents: string[] = [];
    private lastUpdateTime: number = 0;
    private updateTimes: number[] = [];
    private readonly maxSamples: number = 60; // Track last 60 frames
    
    constructor(name: string, priority: number, requiredComponents: string[] = []) {
        this.name = name;
        this.priority = priority;
        this.requiredComponents = requiredComponents;
    }
    
    /**
     * Initialize the system
     * Override in derived classes if needed
     */
    public async initialize(): Promise<void> {
        // Default implementation - no initialization needed
    }
    
    /**
     * Update system logic each frame
     * Must be implemented by derived classes
     */
    public abstract update(entities: IGameEntity[], deltaTime: number): void;
    
    /**
     * Cleanup system resources
     * Override in derived classes if needed
     */
    public cleanup(): void {
        // Default implementation - no cleanup needed
    }
    
    /**
     * Get system status for debugging
     */
    public getStatus(): SystemStatus {
        const averageUpdateTime = this.updateTimes.length > 0 
            ? this.updateTimes.reduce((sum, time) => sum + time, 0) / this.updateTimes.length 
            : 0;
            
        return {
            name: this.name,
            enabled: this.enabled,
            lastUpdateTime: this.lastUpdateTime,
            averageUpdateTime
        };
    }
    
    /**
     * Filter entities that have all required components
     */
    protected filterEntities(entities: IGameEntity[]): IGameEntity[] {
        if (this.requiredComponents.length === 0) {
            return entities.filter(entity => entity.active);
        }
        
        return entities.filter(entity => 
            entity.active && 
            this.requiredComponents.every(componentType => 
                entity.hasComponent(componentType)
            )
        );
    }
    
    /**
     * Record update time for performance monitoring
     */
    protected recordUpdateTime(startTime: number): void {
        const updateTime = performance.now() - startTime;
        this.lastUpdateTime = updateTime;
        
        this.updateTimes.push(updateTime);
        if (this.updateTimes.length > this.maxSamples) {
            this.updateTimes.shift();
        }
    }
    
    /**
     * Safe update wrapper that includes performance monitoring and error handling
     */
    public safeUpdate(entities: IGameEntity[], deltaTime: number): void {
        if (!this.enabled) {return;}
        
        const startTime = performance.now();
        
        try {
            this.update(entities, deltaTime);
        } catch (error) {
            console.error(`Error in system ${this.name}:`, error);
        } finally {
            this.recordUpdateTime(startTime);
        }
    }
    
    /**
     * Enable the system
     */
    public enable(): void {
        this.enabled = true;
    }
    
    /**
     * Disable the system
     */
    public disable(): void {
        this.enabled = false;
    }
    
    /**
     * Toggle system enabled state
     */
    public toggle(): void {
        this.enabled = !this.enabled;
    }
    
    /**
     * Get average update time in milliseconds
     */
    public getAverageUpdateTime(): number {
        return this.updateTimes.length > 0 
            ? this.updateTimes.reduce((sum, time) => sum + time, 0) / this.updateTimes.length 
            : 0;
    }
    
    /**
     * Get required component types for this system
     */
    public getRequiredComponents(): string[] {
        return [...this.requiredComponents];
    }
}
