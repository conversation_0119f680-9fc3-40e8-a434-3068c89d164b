/**
 * BaseComponent - Abstract base class for all game components
 * Provides common functionality and enforces the IComponent interface
 */

import type { IComponent, IGameEntity, IRenderer } from '@/types';

export abstract class BaseComponent implements IComponent {
    public readonly entity: IGameEntity;
    public readonly type: string;
    public enabled: boolean = true;
    
    constructor(entity: IGameEntity, type: string) {
        this.entity = entity;
        this.type = type;
    }
    
    /**
     * Update component logic each frame
     * Must be implemented by derived classes
     */
    public abstract update(deltaTime: number): void;
    
    /**
     * Optional render method for components that need custom rendering
     * Override in derived classes if needed
     */
    public render?(renderer: IRenderer): void;
    
    /**
     * Optional cleanup method called when component is removed
     * Override in derived classes if needed
     */
    public destroy?(): void;
    
    /**
     * Serialize component data for save/load
     * Override in derived classes with specific data
     */
    public serialize(): Record<string, any> {
        return {
            type: this.type,
            enabled: this.enabled
        };
    }
    
    /**
     * Enable the component
     */
    public enable(): void {
        this.enabled = true;
    }
    
    /**
     * Disable the component
     */
    public disable(): void {
        this.enabled = false;
    }
    
    /**
     * Toggle component enabled state
     */
    public toggle(): void {
        this.enabled = !this.enabled;
    }
}
