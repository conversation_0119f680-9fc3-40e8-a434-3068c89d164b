/**
 * Main Game class - orchestrates all game systems and manages the game loop
 * Enterprise-grade implementation with comprehensive error handling and performance monitoring
 */

import * as THREE from 'three';
import Stats from 'stats.js';
import { GUI } from 'lil-gui';

import type { 
    GameConfig, 
    ISystem, 
    SystemStatus,
    PerformanceMetrics 
} from '@/types';

import { Renderer } from '@/graphics/Renderer.ts';
import { StateManager } from './StateManager';
import { EntityManager } from './EntityManager';
import { AssetLoader } from './AssetLoader';
import { PhysicsSystem } from '../systems/PhysicsSystem';
import { RenderSystem } from '../systems/RenderSystem';
import { MovementSystem } from '../systems/MovementSystem';
import { CollisionSystem } from '../systems/CollisionSystem';
import { WeaponSystem } from '../systems/WeaponSystem';

// Temporary type declarations for JS modules
// These should be converted to TypeScript later
declare class InputManager {
    constructor();
    init(): Promise<void>;
    update(deltaTime: number): void;
    destroy(): void;
}

declare class AudioManager {
    constructor();
    init(): Promise<void>;
    update(deltaTime: number): void;
    destroy(): void;
}

declare class UIManager {
    constructor();
    init(): Promise<void>;
    update(deltaTime: number): void;
    destroy(): void;
}

export class Game {
    // Core configuration
    private readonly config: GameConfig;
    private readonly canvas: HTMLCanvasElement;
    
    // Engine systems
    private renderer!: Renderer;
    private inputManager!: InputManager;
    private audioManager!: AudioManager;
    private stateManager!: StateManager;
    private entityManager!: EntityManager;
    private assetLoader!: AssetLoader;
    private uiManager!: UIManager;
    
    // Game systems
    private systems: Map<string, ISystem> = new Map();
    private systemsArray: ISystem[] = [];
    
    // Game loop management
    private isRunning: boolean = false;
    private isPaused: boolean = false;
    private clock: THREE.Clock;
    private lastFrameTime: number = 0;
    private frameCount: number = 0;
    
    // Performance monitoring
    private stats: Stats | null = null;
    private debugGUI: GUI | null = null;
    private performanceMetrics: PerformanceMetrics = {
        fps: 0,
        frameTime: 0,
        memoryUsage: 0,
        drawCalls: 0,
        triangles: 0
    };
    
    // Game state
    private currentScene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    
    constructor(canvas: HTMLCanvasElement, config: GameConfig) {
        this.canvas = canvas;
        this.config = config;
        this.clock = new THREE.Clock();
        this.currentScene = new THREE.Scene();
        
        // Initialize camera for 2.5D perspective
        this.camera = new THREE.PerspectiveCamera(
            75, // Field of view
            window.innerWidth / window.innerHeight, // Aspect ratio
            0.1, // Near plane
            1000 // Far plane
        );
        
        // Position camera for top-down view with slight angle for 2.5D effect
        this.camera.position.set(0, 10, 5);
        this.camera.lookAt(0, 0, 0);
        
        this.bindMethods();
    }
    
    /**
     * Bind methods to maintain proper context
     */
    private bindMethods(): void {
        this.gameLoop = this.gameLoop.bind(this);
        this.handleResize = this.handleResize.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    }
    
    /**
     * Initialize the game engine and all systems
     */
    public async initialize(): Promise<void> {
        try {
            // eslint-disable-next-line no-console
            console.log('Initializing game engine...');
            
            // Initialize performance monitoring
            if (this.config.performanceMonitor) {
                this.initializePerformanceMonitoring();
            }
            
            // Initialize core systems
            await this.initializeCoreComponents();
            
            // Initialize game systems
            await this.initializeGameSystems();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize asset loading
            await this.loadInitialAssets();
            
            // Initialize game state
            await this.initializeGameState();
            
            // eslint-disable-next-line no-console
            console.log('Game engine initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            throw error;
        }
    }
    
    /**
     * Initialize performance monitoring tools
     */
    private initializePerformanceMonitoring(): void {
        if (this.config.debug) {
            // Initialize Stats.js
            this.stats = new Stats();
            this.stats.showPanel(0); // FPS panel
            document.body.appendChild(this.stats.dom);
            
            // Initialize debug GUI
            this.debugGUI = new GUI();
            this.setupDebugGUI();
        }
    }
    
    /**
     * Initialize core engine components
     */
    private async initializeCoreComponents(): Promise<void> {
        // Initialize renderer
        this.renderer = new Renderer(this.canvas);
        await this.renderer.init();
        
        // Initialize input manager
        this.inputManager = new InputManager();
        await this.inputManager.init();
        
        // Initialize audio manager
        this.audioManager = new AudioManager();
        await this.audioManager.init();
        
        // Initialize state manager
        this.stateManager = new StateManager();
        await this.stateManager.init();
        
        // Initialize entity manager
        this.entityManager = new EntityManager();
        await this.entityManager.init();
        
        // Initialize asset loader
        this.assetLoader = new AssetLoader();
        await this.assetLoader.init();
        
        // Initialize UI manager
        this.uiManager = new UIManager();
        await this.uiManager.init();
    }
    
    /**
     * Initialize game systems in priority order
     */
    private async initializeGameSystems(): Promise<void> {
        // Create systems
        const physicsSystem = new PhysicsSystem();
        const renderSystem = new RenderSystem(this.renderer, this.camera);
        const movementSystem = new MovementSystem();
        const collisionSystem = new CollisionSystem();
        const weaponSystem = new WeaponSystem();
        
        // Add systems to manager
        this.addSystem('physics', physicsSystem);
        this.addSystem('movement', movementSystem);
        this.addSystem('collision', collisionSystem);
        this.addSystem('weapon', weaponSystem);
        this.addSystem('render', renderSystem);
        
        // Initialize all systems
        for (const system of this.systemsArray) {
            if (system.initialize) {
                await system.initialize();
            }
        }
    }
    
    /**
     * Add a system to the game engine
     */
    private addSystem(name: string, system: ISystem): void {
        this.systems.set(name, system);
        this.systemsArray.push(system);
        
        // Sort systems by priority
        this.systemsArray.sort((a, b) => a.priority - b.priority);
    }
    
    /**
     * Set up event listeners
     */
    private setupEventListeners(): void {
        window.addEventListener('resize', this.handleResize);
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Prevent context menu on right click
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    /**
     * Load initial game assets
     */
    private async loadInitialAssets(): Promise<void> {
        // Load essential assets
        // Note: Asset paths would be defined in a manifest file
        // eslint-disable-next-line no-console
        console.log('Loading initial assets...');
        
        // This would load textures, models, audio, etc.
        // For now, we'll set up basic placeholder assets
        
        // eslint-disable-next-line no-console
        console.log('Initial assets loaded');
    }
    
    /**
     * Initialize game state and scene
     */
    private async initializeGameState(): Promise<void> {
        // Set initial game state
        this.stateManager.changeState('menu');
        
        // Set up scene lighting
        this.setupSceneLighting();
        
        // Create initial game world
        this.setupGameWorld();
    }
    
    /**
     * Set up scene lighting for 2.5D effect
     */
    private setupSceneLighting(): void {
        // Ambient light for general illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.currentScene.add(ambientLight);
        
        // Directional light for main lighting and shadows
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.currentScene.add(directionalLight);
        
        // Point light for dramatic effect
        const pointLight = new THREE.PointLight(0x00ffff, 0.5, 20);
        pointLight.position.set(0, 5, 0);
        this.currentScene.add(pointLight);
    }
    
    /**
     * Set up basic game world
     */
    private setupGameWorld(): void {
        // Create a basic ground plane
        const groundGeometry = new THREE.PlaneGeometry(50, 50);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.currentScene.add(ground);
    }
    
    /**
     * Setup debug GUI controls
     */
    private setupDebugGUI(): void {
        if (!this.debugGUI) {
            return;
        }
        
        const gameFolder = this.debugGUI.addFolder('Game');
        gameFolder.add(this, 'isPaused').name('Paused');
        gameFolder.add(this.performanceMetrics, 'fps').name('FPS').listen();
        gameFolder.add(this.performanceMetrics, 'frameTime').name('Frame Time (ms)').listen();
        
        const systemsFolder = this.debugGUI.addFolder('Systems');
        this.systemsArray.forEach(system => {
            systemsFolder.add(system, 'enabled').name(system.name);
        });
        
        const cameraFolder = this.debugGUI.addFolder('Camera');
        cameraFolder.add(this.camera.position, 'x', -20, 20).name('X');
        cameraFolder.add(this.camera.position, 'y', 1, 20).name('Y');
        cameraFolder.add(this.camera.position, 'z', -20, 20).name('Z');
    }
    
    /**
     * Start the game loop
     */
    public start(): void {
        if (this.isRunning) {
            return;
        }
        
        this.isRunning = true;
        this.clock.start();
        this.requestFrame();
        
        // eslint-disable-next-line no-console
        console.log('Game started');
    }
    
    /**
     * Stop the game loop
     */
    public stop(): void {
        this.isRunning = false;
        // eslint-disable-next-line no-console
        console.log('Game stopped');
    }
    
    /**
     * Pause the game
     */
    public pause(): void {
        this.isPaused = true;
        this.stateManager.pushState('paused');
        // eslint-disable-next-line no-console
        console.log('Game paused');
    }
    
    /**
     * Resume the game
     */
    public resume(): void {
        this.isPaused = false;
        this.stateManager.popState();
        // eslint-disable-next-line no-console
        console.log('Game resumed');
    }
    
    /**
     * Request next animation frame
     */
    private requestFrame(): void {
        if (this.isRunning) {
            requestAnimationFrame(this.gameLoop);
        }
    }
    
    /**
     * Main game loop
     */
    private gameLoop(): void {
        if (this.stats) {
            this.stats.begin();
        }
        
        const currentTime = performance.now();
        const deltaTime = this.clock.getDelta();
        
        // Update performance metrics
        this.updatePerformanceMetrics(currentTime, deltaTime);
        
        // Update game only if not paused
        if (!this.isPaused) {
            this.update(deltaTime);
        }
        
        // Always render (for UI updates even when paused)
        this.render();
        
        if (this.stats) {
            this.stats.end();
        }
        
        this.lastFrameTime = currentTime;
        this.requestFrame();
    }
    
    /**
     * Update all game systems
     */
    private update(deltaTime: number): void {
        // Update input manager
        this.inputManager.update(deltaTime);
        
        // Get all entities from entity manager
        const entities = this.entityManager.getAllEntities();
        
        // Update state manager
        this.stateManager.update(entities, deltaTime);
        
        // Update all systems
        for (const system of this.systemsArray) {
            if (system.enabled) {
                try {
                    system.update(entities, deltaTime);
                } catch (error) {
                    // eslint-disable-next-line no-console
                    console.error(`Error in system ${system.name}:`, error);
                }
            }
        }
        
        // Update UI
        this.uiManager.update(deltaTime);
    }
    
    /**
     * Render the current frame
     */
    private render(): void {
        if (this.renderer && this.currentScene && this.camera) {
            this.renderer.render(this.currentScene, this.camera);
        }
    }
    
    /**
     * Update performance metrics
     */
    private updatePerformanceMetrics(currentTime: number, deltaTime: number): void {
        this.frameCount++;
        
        if (this.frameCount % 60 === 0) { // Update every 60 frames
            this.performanceMetrics.fps = Math.round(1 / deltaTime);
            this.performanceMetrics.frameTime = Math.round(deltaTime * 1000);
            
            // Get renderer info if available
            const rendererInfo = this.renderer?.getInfo();
            if (rendererInfo && typeof rendererInfo === 'object' && 'info' in rendererInfo) {
                interface RendererInfo {
                    render?: {
                        calls?: number;
                        triangles?: number;
                    };
                }
                const info = rendererInfo.info as RendererInfo;
                this.performanceMetrics.drawCalls = info.render?.calls || 0;
                this.performanceMetrics.triangles = info.render?.triangles || 0;
            }
            
            // Get memory usage (if available)
            if ('memory' in performance) {
                interface PerformanceMemory {
                    memory: {
                        usedJSHeapSize: number;
                    };
                }
                this.performanceMetrics.memoryUsage = (performance as PerformanceMemory).memory.usedJSHeapSize / 1048576; // MB
            }
        }
    }
    
    /**
     * Handle window resize
     */
    private handleResize(): void {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Update camera aspect ratio
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        // Update renderer size
        if (this.renderer) {
            this.renderer.handleResize();
        }
    }
    
    /**
     * Handle visibility change (tab focus/blur)
     */
    private handleVisibilityChange(): void {
        if (document.hidden) {
            if (this.isRunning && !this.isPaused) {
                this.pause();
            }
        }
    }
    
    /**
     * Get current performance metrics
     */
    public getPerformanceMetrics(): PerformanceMetrics {
        return { ...this.performanceMetrics };
    }
    
    /**
     * Get system status for debugging
     */
    public getSystemStatus(): Map<string, SystemStatus> {
        const status = new Map<string, SystemStatus>();
        
        this.systems.forEach((system, name) => {
            status.set(name, {
                name: system.name,
                enabled: system.enabled,
                entityCount: this.entityManager.getEntityCount(),
                lastUpdateTime: performance.now()
            });
        });
        
        return status;
    }
    
    /**
     * Get current scene
     */
    public getScene(): THREE.Scene {
        return this.currentScene;
    }
    
    /**
     * Get camera
     */
    public getCamera(): THREE.PerspectiveCamera {
        return this.camera;
    }
    
    /**
     * Get entity manager
     */
    public getEntityManager(): EntityManager {
        return this.entityManager;
    }
    
    /**
     * Get input manager
     */
    public getInputManager(): InputManager {
        return this.inputManager;
    }
    
    /**
     * Get audio manager
     */
    public getAudioManager(): AudioManager {
        return this.audioManager;
    }
    
    /**
     * Get state manager
     */
    public getStateManager(): StateManager {
        return this.stateManager;
    }
    
    /**
     * Clean up and destroy the game
     */
    public destroy(): void {
        // eslint-disable-next-line no-console
        console.log('Destroying game...');
        
        // Stop game loop
        this.stop();
        
        // Cleanup systems
        this.systemsArray.forEach(system => {
            if (system.cleanup) {
                system.cleanup();
            }
        });
        
        // Cleanup core components
        this.renderer?.destroy();
        this.inputManager?.destroy();
        this.audioManager?.destroy();
        this.uiManager?.destroy();
        this.entityManager?.destroy();
        
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Cleanup performance monitoring
        if (this.stats) {
            document.body.removeChild(this.stats.dom);
        }
        
        if (this.debugGUI) {
            this.debugGUI.destroy();
        }
        
        // eslint-disable-next-line no-console
        console.log('Game destroyed');
    }
}
