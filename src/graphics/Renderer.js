import * as THREE from 'three';

export class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.renderer = null;
        this.composer = null;
    }

    async init() {
        // Create WebGL renderer
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: false,
        });

        // Configure renderer
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.setClearColor(0x0a0a0a, 1);

        // Enable shadows for 2.5D effect
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Enable tone mapping for better color representation
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;

        // Enable gamma correction
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;

        console.log('Renderer initialized');
    }

    render(scene, camera) {
        if (this.renderer && scene && camera) {
            this.renderer.render(scene, camera);
        }
    }

    handleResize() {
        if (this.renderer) {
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }
    }

    destroy() {
        if (this.renderer) {
            this.renderer.dispose();
            this.renderer = null;
        }
        console.log('Renderer destroyed');
    }

    // Getters
    getRenderer() {
        return this.renderer;
    }
}
