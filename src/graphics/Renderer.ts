import * as THREE from 'three';

import type { RendererConfig } from '@/types';

/**
 * Enterprise-grade graphics renderer class for bullet hell game
 * Manages WebGL rendering, post-processing, and performance optimization
 */
export class Renderer {
    private canvas: HTMLCanvasElement;
    private renderer: THREE.WebGLRenderer | null = null;
    private composer: THREE.EffectComposer | null = null;
    private config: RendererConfig;

    constructor(canvas: HTMLCanvasElement, config?: Partial<RendererConfig>) {
        this.canvas = canvas;
        this.config = {
            antialias: true,
            alpha: false,
            shadowMapEnabled: true,
            shadowMapType: THREE.PCFSoftShadowMap,
            toneMapping: THREE.ACESFilmicToneMapping,
            toneMappingExposure: 1.0,
            outputColorSpace: THREE.SRGBColorSpace,
            clearColor: 0x0a0a0a,
            clearAlpha: 1,
            maxPixelRatio: 2,
            ...config,
        };
    }

    /**
     * Initialize the renderer with enterprise-grade configuration
     */
    public async init(): Promise<void> {
        try {
            // Create WebGL renderer
            this.renderer = new THREE.WebGLRenderer({
                canvas: this.canvas,
                antialias: this.config.antialias,
                alpha: this.config.alpha,
                powerPreference: 'high-performance',
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
            });

            // Configure renderer properties
            this.configureRenderer();

            // Configure shadows for 2.5D effect
            this.configureShadows();

            // Configure post-processing
            this.configurePostProcessing();

            // Verify WebGL context
            this.verifyWebGLContext();

            console.log('Renderer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize renderer:', error);
            throw new Error(
                `Renderer initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
        }
    }

    /**
     * Configure renderer settings
     */
    private configureRenderer(): void {
        if (!this.renderer) {
            throw new Error('Renderer not initialized');
        }

        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.config.maxPixelRatio));
        this.renderer.setClearColor(this.config.clearColor, this.config.clearAlpha);

        // Enable tone mapping for better color representation
        this.renderer.toneMapping = this.config.toneMapping;
        this.renderer.toneMappingExposure = this.config.toneMappingExposure;

        // Enable gamma correction
        this.renderer.outputColorSpace = this.config.outputColorSpace;
    }

    /**
     * Configure shadow settings
     */
    private configureShadows(): void {
        if (!this.renderer) {
            return;
        }

        this.renderer.shadowMap.enabled = this.config.shadowMapEnabled;
        this.renderer.shadowMap.type = this.config.shadowMapType;
    }

    /**
     * Configure post-processing pipeline
     */
    private configurePostProcessing(): void {
        // Post-processing setup will be implemented later
        // This is a placeholder for future enhancement
    }

    /**
     * Verify WebGL context and capabilities
     */
    private verifyWebGLContext(): void {
        if (!this.renderer) {
            throw new Error('Renderer not available for verification');
        }

        const gl = this.renderer.getContext();
        const capabilities = this.renderer.capabilities;

        console.log('WebGL Capabilities:', {
            version: gl.getParameter(gl.VERSION),
            vendor: gl.getParameter(gl.VENDOR),
            renderer: gl.getParameter(gl.RENDERER),
            maxTextures: capabilities.maxTextures,
            maxVertexTextures: capabilities.maxVertexTextures,
            maxTextureSize: capabilities.maxTextureSize,
            maxCubemapSize: capabilities.maxCubemapSize,
        });
    }

    /**
     * Render a scene with the given camera
     */
    public render(scene: THREE.Scene, camera: THREE.Camera): void {
        if (!this.renderer || !scene || !camera) {
            console.warn('Cannot render: missing renderer, scene, or camera');
            return;
        }

        try {
            if (this.composer) {
                this.composer.render();
            } else {
                this.renderer.render(scene, camera);
            }
        } catch (error) {
            console.error('Render error:', error);
        }
    }

    /**
     * Handle window resize events
     */
    public handleResize(): void {
        if (!this.renderer) {
            return;
        }

        const width = window.innerWidth;
        const height = window.innerHeight;

        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.config.maxPixelRatio));

        if (this.composer) {
            this.composer.setSize(width, height);
        }
    }

    /**
     * Clean up resources
     */
    public destroy(): void {
        if (this.composer) {
            this.composer.dispose();
            this.composer = null;
        }

        if (this.renderer) {
            this.renderer.dispose();
            this.renderer = null;
        }

        console.log('Renderer destroyed');
    }

    /**
     * Get the Three.js renderer instance
     */
    public getRenderer(): THREE.WebGLRenderer | null {
        return this.renderer;
    }

    /**
     * Get renderer information for debugging
     */
    public getInfo(): object {
        if (!this.renderer) {
            return { status: 'not_initialized' };
        }

        return {
            status: 'initialized',
            info: this.renderer.info,
            capabilities: {
                maxTextures: this.renderer.capabilities.maxTextures,
                maxVertexTextures: this.renderer.capabilities.maxVertexTextures,
                maxTextureSize: this.renderer.capabilities.maxTextureSize,
            },
        };
    }
}
