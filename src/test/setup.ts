/**
 * Test Setup Configuration
 * Vitest setup file for game testing environment
 */

import { vi } from 'vitest';

// Mock Three.js for testing environment
vi.mock('three', () => {
    return {
        WebGLRenderer: vi.fn(() => ({
            setSize: vi.fn(),
            render: vi.fn(),
            dispose: vi.fn(),
            domElement: document.createElement('canvas'),
        })),
        Scene: vi.fn(() => ({
            add: vi.fn(),
            remove: vi.fn(),
            traverse: vi.fn(),
        })),
        PerspectiveCamera: vi.fn(() => ({
            position: { set: vi.fn() },
            lookAt: vi.fn(),
            updateProjectionMatrix: vi.fn(),
        })),
        Clock: vi.fn(() => ({
            getDelta: vi.fn(() => 0.016),
            getElapsedTime: vi.fn(() => 1.0),
        })),
        Vector3: vi.fn(() => ({
            set: vi.fn(),
            add: vi.fn(),
            sub: vi.fn(),
            length: vi.fn(() => 1.0),
        })),
        Object3D: vi.fn(() => ({
            position: { set: vi.fn() },
            rotation: { set: vi.fn() },
            scale: { set: vi.fn() },
        })),
    };
});

// Mock Cannon.js for physics testing
vi.mock('cannon-es', () => {
    return {
        World: vi.fn(() => ({
            gravity: { set: vi.fn() },
            step: vi.fn(),
            addBody: vi.fn(),
            removeBody: vi.fn(),
        })),
        Body: vi.fn(() => ({
            position: { set: vi.fn() },
            velocity: { set: vi.fn() },
            addEventListener: vi.fn(),
        })),
        Box: vi.fn(),
        Sphere: vi.fn(),
        Vec3: vi.fn(() => ({
            set: vi.fn(),
        })),
    };
});

// Mock Web Audio API
const mockAudioContext = {
    createGain: vi.fn(() => ({
        connect: vi.fn(),
        gain: { value: 1 },
    })),
    createBufferSource: vi.fn(() => ({
        connect: vi.fn(),
        start: vi.fn(),
        stop: vi.fn(),
    })),
    decodeAudioData: vi.fn(() => Promise.resolve({})),
    destination: {},
    state: 'running',
};

Object.defineProperty(window, 'AudioContext', {
    value: vi.fn(() => mockAudioContext),
});

Object.defineProperty(window, 'webkitAudioContext', {
    value: vi.fn(() => mockAudioContext),
});

// Mock requestAnimationFrame
Object.defineProperty(window, 'requestAnimationFrame', {
    value: vi.fn((callback: FrameRequestCallback) => {
        return setTimeout(() => callback(Date.now()), 16);
    }),
});

Object.defineProperty(window, 'cancelAnimationFrame', {
    value: vi.fn((id: number) => clearTimeout(id)),
});

// Mock performance API
Object.defineProperty(window, 'performance', {
    value: {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        memory: {
            usedJSHeapSize: 1000000,
            totalJSHeapSize: 2000000,
            jsHeapSizeLimit: 4000000,
        },
    },
});

// Setup console warnings for tests
const originalWarn = console.warn;
console.warn = (...args) => {
    // Suppress known Three.js warnings in tests
    if (typeof args[0] === 'string' && args[0].includes('THREE.')) {
        return;
    }
    originalWarn.call(console, ...args);
};

// Global test timeout
vi.setConfig({
    testTimeout: 10000,
});
