export class InputManager {
    constructor() {
        this.keys = new Map();
        this.mousePosition = { x: 0, y: 0 };
        this.mouseButtons = new Map();
        this.gamepadState = null;

        // Input bindings
        this.bindings = {
            // Movement
            moveUp: ['KeyW', 'ArrowUp'],
            moveDown: ['KeyS', 'ArrowDown'],
            moveLeft: ['KeyA', 'ArrowLeft'],
            moveRight: ['KeyD', 'ArrowRight'],

            // Actions
            shoot: ['Space', 'Mouse0'],
            specialAttack: ['KeyE', 'Mouse2'],
            pause: ['Escape', 'KeyP'],
            interact: ['KeyF', 'Enter'],
        };

        // Bind methods
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleContextMenu = this.handleContextMenu.bind(this);
    }

    async init() {
        // Add event listeners
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
        document.addEventListener('mousedown', this.handleMouseDown);
        document.addEventListener('mouseup', this.handleMouseUp);
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('contextmenu', this.handleContextMenu);

        // Check for gamepad support
        if ('getGamepads' in navigator) {
            console.log('Gamepad support detected');
        }

        console.log('InputManager initialized');
    }

    update(_deltaTime) {
        this.updateGamepad();
    }

    // Keyboard handling
    handleKeyDown(event) {
        if (event.repeat) {
            return;
        }

        this.keys.set(event.code, {
            pressed: true,
            justPressed: true,
            timestamp: performance.now(),
        });

        // Prevent default for game keys
        if (this.isGameKey(event.code)) {
            event.preventDefault();
        }
    }

    handleKeyUp(event) {
        this.keys.set(event.code, {
            pressed: false,
            justPressed: false,
            justReleased: true,
            timestamp: performance.now(),
        });
    }

    // Mouse handling
    handleMouseDown(event) {
        const buttonKey = `Mouse${event.button}`;
        this.mouseButtons.set(buttonKey, {
            pressed: true,
            justPressed: true,
            timestamp: performance.now(),
        });

        // Prevent default for game mouse buttons
        if (this.isGameMouseButton(buttonKey)) {
            event.preventDefault();
        }
    }

    handleMouseUp(event) {
        const buttonKey = `Mouse${event.button}`;
        this.mouseButtons.set(buttonKey, {
            pressed: false,
            justPressed: false,
            justReleased: true,
            timestamp: performance.now(),
        });
    }

    handleMouseMove(event) {
        this.mousePosition.x = event.clientX;
        this.mousePosition.y = event.clientY;
    }

    handleContextMenu(event) {
        event.preventDefault(); // Disable right-click context menu
    }

    // Gamepad handling
    updateGamepad() {
        if ('getGamepads' in navigator) {
            const gamepads = navigator.getGamepads();
            if (gamepads[0]) {
                this.gamepadState = {
                    leftStick: {
                        x: gamepads[0].axes[0],
                        y: gamepads[0].axes[1],
                    },
                    rightStick: {
                        x: gamepads[0].axes[2],
                        y: gamepads[0].axes[3],
                    },
                    buttons: gamepads[0].buttons.map(button => ({
                        pressed: button.pressed,
                        value: button.value,
                    })),
                };
            }
        }
    }

    // Input query methods
    isPressed(action) {
        const inputs = this.bindings[action];
        if (!inputs) {
            return false;
        }

        return inputs.some(input => {
            if (input.startsWith('Mouse')) {
                const mouseState = this.mouseButtons.get(input);
                return mouseState?.pressed || false;
            } else {
                const keyState = this.keys.get(input);
                return keyState?.pressed || false;
            }
        });
    }

    isJustPressed(action) {
        const inputs = this.bindings[action];
        if (!inputs) {
            return false;
        }

        return inputs.some(input => {
            if (input.startsWith('Mouse')) {
                const mouseState = this.mouseButtons.get(input);
                const result = mouseState?.justPressed || false;
                // Clear just pressed state
                if (result && mouseState) {
                    mouseState.justPressed = false;
                }
                return result;
            } else {
                const keyState = this.keys.get(input);
                const result = keyState?.justPressed || false;
                // Clear just pressed state
                if (result && keyState) {
                    keyState.justPressed = false;
                }
                return result;
            }
        });
    }

    isJustReleased(action) {
        const inputs = this.bindings[action];
        if (!inputs) {
            return false;
        }

        return inputs.some(input => {
            if (input.startsWith('Mouse')) {
                const mouseState = this.mouseButtons.get(input);
                const result = mouseState?.justReleased || false;
                // Clear just released state
                if (result && mouseState) {
                    mouseState.justReleased = false;
                }
                return result;
            } else {
                const keyState = this.keys.get(input);
                const result = keyState?.justReleased || false;
                // Clear just released state
                if (result && keyState) {
                    keyState.justReleased = false;
                }
                return result;
            }
        });
    }

    // Movement vector calculation
    getMovementVector() {
        const vector = { x: 0, y: 0 };

        if (this.isPressed('moveLeft')) {
            vector.x -= 1;
        }
        if (this.isPressed('moveRight')) {
            vector.x += 1;
        }
        if (this.isPressed('moveUp')) {
            vector.y += 1;
        }
        if (this.isPressed('moveDown')) {
            vector.y -= 1;
        }

        // Add gamepad input
        if (this.gamepadState?.leftStick) {
            vector.x += this.gamepadState.leftStick.x;
            vector.y -= this.gamepadState.leftStick.y; // Invert Y for screen coordinates
        }

        // Normalize diagonal movement
        const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y);
        if (magnitude > 1) {
            vector.x /= magnitude;
            vector.y /= magnitude;
        }

        return vector;
    }

    // Mouse position in normalized screen coordinates (-1 to 1)
    getNormalizedMousePosition() {
        return {
            x: (this.mousePosition.x / window.innerWidth) * 2 - 1,
            y: -(this.mousePosition.y / window.innerHeight) * 2 + 1,
        };
    }

    // Utility methods
    isGameKey(code) {
        return Object.values(this.bindings).flat().includes(code);
    }

    isGameMouseButton(button) {
        return Object.values(this.bindings).flat().includes(button);
    }

    // Cleanup
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('mousedown', this.handleMouseDown);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('contextmenu', this.handleContextMenu);

        this.keys.clear();
        this.mouseButtons.clear();

        console.log('InputManager destroyed');
    }
}
