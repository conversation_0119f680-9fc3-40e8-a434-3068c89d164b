import { Howl, Howler } from 'howler';

export class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.music = new Map();
        this.currentMusic = null;

        // Audio settings
        this.masterVolume = 1.0;
        this.musicVolume = 0.7;
        this.sfxVolume = 0.8;

        // Audio files configuration
        this.audioConfig = {
            music: {
                menu: { src: '/assets/audio/music/menu.mp3', loop: true },
                gameplay: { src: '/assets/audio/music/gameplay.mp3', loop: true },
                boss: { src: '/assets/audio/music/boss.mp3', loop: true },
            },
            sfx: {
                playerShoot: { src: '/assets/audio/sfx/player_shoot.wav' },
                enemyShoot: { src: '/assets/audio/sfx/enemy_shoot.wav' },
                playerHit: { src: '/assets/audio/sfx/player_hit.wav' },
                enemyHit: { src: '/assets/audio/sfx/enemy_hit.wav' },
                enemyDestroy: { src: '/assets/audio/sfx/enemy_destroy.wav' },
                powerUp: { src: '/assets/audio/sfx/power_up.wav' },
                explosion: { src: '/assets/audio/sfx/explosion.wav' },
                menuSelect: { src: '/assets/audio/sfx/menu_select.wav' },
                menuConfirm: { src: '/assets/audio/sfx/menu_confirm.wav' },
            },
        };
    }

    async init() {
        try {
            // Set global audio settings
            Howler.volume(this.masterVolume);

            // Load audio files
            await this.loadAudioFiles();

            console.log('AudioManager initialized');
        } catch (error) {
            console.warn('AudioManager initialization failed:', error);
            // Continue without audio if loading fails
        }
    }

    async loadAudioFiles() {
        const loadPromises = [];

        // Load music
        for (const [key, config] of Object.entries(this.audioConfig.music)) {
            const promise = this.loadSound(key, config, 'music');
            loadPromises.push(promise);
        }

        // Load sound effects
        for (const [key, config] of Object.entries(this.audioConfig.sfx)) {
            const promise = this.loadSound(key, config, 'sfx');
            loadPromises.push(promise);
        }

        // Wait for all audio to load (with timeout)
        try {
            await Promise.allSettled(loadPromises);
            console.log('Audio files loaded');
        } catch (error) {
            console.warn('Some audio files failed to load:', error);
        }
    }

    loadSound(key, config, type) {
        return new Promise((resolve, _reject) => {
            const sound = new Howl({
                src: [config.src],
                loop: config.loop || false,
                volume: type === 'music' ? this.musicVolume : this.sfxVolume,
                onload: () => resolve(sound),
                onloaderror: (id, error) => {
                    console.warn(`Failed to load ${type} ${key}:`, error);
                    resolve(null); // Resolve with null instead of rejecting
                },
            });

            if (type === 'music') {
                this.music.set(key, sound);
            } else {
                this.sounds.set(key, sound);
            }
        });
    }

    // Music control
    playMusic(key, fadeIn = true) {
        const music = this.music.get(key);
        if (!music) {
            console.warn(`Music ${key} not found`);
            return;
        }

        // Stop current music
        if (this.currentMusic) {
            this.stopMusic(true);
        }

        this.currentMusic = music;

        if (fadeIn) {
            music.volume(0);
            music.play();
            music.fade(0, this.musicVolume, 1000);
        } else {
            music.volume(this.musicVolume);
            music.play();
        }
    }

    stopMusic(fadeOut = true) {
        if (!this.currentMusic) {
            return;
        }

        if (fadeOut) {
            this.currentMusic.fade(this.currentMusic.volume(), 0, 1000);
            setTimeout(() => {
                if (this.currentMusic) {
                    this.currentMusic.stop();
                    this.currentMusic = null;
                }
            }, 1000);
        } else {
            this.currentMusic.stop();
            this.currentMusic = null;
        }
    }

    pauseMusic() {
        if (this.currentMusic) {
            this.currentMusic.pause();
        }
    }

    resumeMusic() {
        if (this.currentMusic) {
            this.currentMusic.play();
        }
    }

    // Sound effects
    playSound(key, options = {}) {
        const sound = this.sounds.get(key);
        if (!sound) {
            console.warn(`Sound ${key} not found`);
            return;
        }

        const volume = options.volume !== undefined ? options.volume : this.sfxVolume;
        const rate = options.rate || 1.0;
        const loop = options.loop || false;

        const id = sound.play();
        sound.volume(volume, id);
        sound.rate(rate, id);
        sound.loop(loop, id);

        return id;
    }

    stopSound(key, id = null) {
        const sound = this.sounds.get(key);
        if (!sound) {
            return;
        }

        if (id !== null) {
            sound.stop(id);
        } else {
            sound.stop();
        }
    }

    // Volume controls
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        Howler.volume(this.masterVolume);
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));

        // Update current music volume
        if (this.currentMusic) {
            this.currentMusic.volume(this.musicVolume);
        }

        // Update all music volumes
        this.music.forEach(music => {
            if (music !== this.currentMusic) {
                music.volume(this.musicVolume);
            }
        });
    }

    setSfxVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));

        // Update all sound effect volumes
        this.sounds.forEach(sound => {
            sound.volume(this.sfxVolume);
        });
    }

    // Utility methods
    getMasterVolume() {
        return this.masterVolume;
    }

    getMusicVolume() {
        return this.musicVolume;
    }

    getSfxVolume() {
        return this.sfxVolume;
    }

    // Presets for common game events
    playPlayerShoot() {
        this.playSound('playerShoot', { volume: 0.6, rate: 1 + Math.random() * 0.2 });
    }

    playEnemyShoot() {
        this.playSound('enemyShoot', { volume: 0.5, rate: 0.8 + Math.random() * 0.4 });
    }

    playPlayerHit() {
        this.playSound('playerHit', { volume: 0.8 });
    }

    playEnemyHit() {
        this.playSound('enemyHit', { volume: 0.7, rate: 0.9 + Math.random() * 0.2 });
    }

    playEnemyDestroy() {
        this.playSound('enemyDestroy', { volume: 0.9 });
    }

    playExplosion() {
        this.playSound('explosion', { volume: 1.0, rate: 0.8 + Math.random() * 0.4 });
    }

    playPowerUp() {
        this.playSound('powerUp', { volume: 0.8 });
    }

    playMenuSelect() {
        this.playSound('menuSelect', { volume: 0.6 });
    }

    playMenuConfirm() {
        this.playSound('menuConfirm', { volume: 0.7 });
    }

    // Cleanup
    destroy() {
        // Stop all sounds
        this.stopMusic(false);
        this.sounds.forEach(sound => sound.unload());
        this.music.forEach(music => music.unload());

        this.sounds.clear();
        this.music.clear();
        this.currentMusic = null;

        console.log('AudioManager destroyed');
    }
}
