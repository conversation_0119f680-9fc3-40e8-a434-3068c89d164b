/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
    height: 100vh;
    color: #ffffff;
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#game-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* UI Overlay */
#ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none;
}

#ui-overlay > * {
    pointer-events: auto;
}

/* HUD Styles */
.hud {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.hud-top {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.hud-bottom {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: center;
}

/* Health Bar */
.health-bar {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #00ff88;
    border-radius: 10px;
    padding: 5px;
    width: 200px;
    position: relative;
}

.health-fill {
    background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #00ff88 100%);
    height: 20px;
    border-radius: 5px;
    width: 100%;
    transition: width 0.3s ease;
}

.health-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Score and Lives */
.score,
.lives {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #00aaff;
    border-radius: 10px;
    padding: 10px 15px;
    font-weight: bold;
}

.score-value,
.lives-value {
    color: #00ff88;
    margin-left: 10px;
}

/* Weapon Info */
.weapon-info {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #ff8800;
    border-radius: 10px;
    padding: 10px 20px;
    text-align: center;
    min-width: 200px;
}

.weapon-name {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #ff8800;
}

.ammo-bar {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
}

.ammo-fill {
    background: linear-gradient(90deg, #ff8800, #ffaa00);
    height: 100%;
    width: 100%;
    transition: width 0.2s ease;
}

/* Menu Styles */
.menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
}

.game-title {
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00ff88, #00aaff, #ff8800);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
    margin-bottom: 10px;
    animation: glow 2s ease-in-out infinite alternate;
}

.game-subtitle {
    font-size: 1.5rem;
    color: #cccccc;
    margin-bottom: 50px;
    font-weight: 400;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.menu-btn {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    padding: 15px 40px;
    background: transparent;
    color: #ffffff;
    border: 2px solid #00aaff;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-transform: uppercase;
}

.menu-btn:hover {
    background: rgba(0, 170, 255, 0.2);
    box-shadow: 0 0 20px rgba(0, 170, 255, 0.5);
    transform: translateY(-2px);
}

.menu-btn.primary {
    border-color: #00ff88;
    color: #00ff88;
}

.menu-btn.primary:hover {
    background: rgba(0, 255, 136, 0.2);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.final-score {
    font-size: 1.5rem;
    margin-bottom: 30px;
    color: #00ff88;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes glow {
    from {
        text-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
    }
    to {
        text-shadow:
            0 0 50px rgba(0, 255, 136, 0.8),
            0 0 60px rgba(0, 170, 255, 0.3);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }

    .hud-top {
        flex-direction: column;
        gap: 10px;
    }

    .health-bar {
        width: 150px;
    }

    .menu-btn {
        font-size: 1rem;
        padding: 12px 30px;
        min-width: 150px;
    }
}
