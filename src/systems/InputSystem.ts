/**
 * InputSystem - Manages input distribution to entities with InputComponent
 * Bridges between InputManager.js and InputComponent.ts for ECS architecture
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2 } from '@/types';
import { InputComponent } from '@/components/InputComponent';

export class InputSystem extends BaseSystem {
    public static readonly NAME = 'InputSystem';
    public static readonly PRIORITY = 0; // Highest priority - process input first
    
    // Reference to global input manager
    private inputManager: any = null;
    
    // Input state tracking
    private previousKeyStates: Map<string, boolean> = new Map();
    private previousMouseStates: Map<number, boolean> = new Map();
    private previousMousePosition: Vector2 = { x: 0, y: 0 };
    
    constructor() {
        super(InputSystem.NAME, InputSystem.PRIORITY, ['input']);
    }
    
    /**
     * Initialize the input system
     */
    public override async initialize(): Promise<void> {
        // Get reference to global input manager
        if (typeof window !== 'undefined' && (window as any).game?.inputManager) {
            this.inputManager = (window as any).game.inputManager;
        }
        
        console.log('InputSystem initialized');
    }
    
    /**
     * Update input system - distribute input events to input components
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        if (!this.inputManager) {return;}
        
        const inputEntities = this.filterEntities(entities);
        
        // Process input for each entity with InputComponent
        for (const entity of inputEntities) {
            const inputComponent = entity.getComponent<InputComponent>('input');
            if (!inputComponent || !inputComponent.isPlayer) {continue;}
            
            // Update input component first
            inputComponent.update(deltaTime);
            
            // Process keyboard input
            this.processKeyboardInput(inputComponent);
            
            // Process mouse input
            this.processMouseInput(inputComponent);
            
            // Process gamepad input (if available)
            this.processGamepadInput(inputComponent);
        }
        
        // Update previous states for next frame
        this.updatePreviousStates();
    }
    
    /**
     * Process keyboard input for an input component
     */
    private processKeyboardInput(inputComponent: InputComponent): void {
        if (!this.inputManager) {return;}
        
        // Check for key down events
        for (const [key, state] of this.inputManager.keys) {
            const wasPressed = this.previousKeyStates.get(key) || false;
            const isPressed = state.pressed || false;
            
            // Key just pressed
            if (isPressed && !wasPressed) {
                inputComponent.handleKeyDown(key);
            }
            
            // Key just released
            if (!isPressed && wasPressed) {
                inputComponent.handleKeyUp(key);
            }
        }
    }
    
    /**
     * Process mouse input for an input component
     */
    private processMouseInput(inputComponent: InputComponent): void {
        if (!this.inputManager) {return;}
        
        // Process mouse button events
        for (const [button, state] of this.inputManager.mouseButtons) {
            const buttonNumber = parseInt(button.replace('Mouse', ''));
            const wasPressed = this.previousMouseStates.get(buttonNumber) || false;
            const isPressed = state.pressed || false;
            
            // Mouse button just pressed
            if (isPressed && !wasPressed) {
                inputComponent.handleMouseDown(buttonNumber);
            }
            
            // Mouse button just released
            if (!isPressed && wasPressed) {
                inputComponent.handleMouseUp(buttonNumber);
            }
        }
        
        // Process mouse movement
        const currentMousePos = this.inputManager.mousePosition;
        const mouseDelta = {
            x: currentMousePos.x - this.previousMousePosition.x,
            y: currentMousePos.y - this.previousMousePosition.y
        };
        
        if (mouseDelta.x !== 0 || mouseDelta.y !== 0) {
            inputComponent.handleMouseMove(currentMousePos, mouseDelta);
        }
    }
    
    /**
     * Process gamepad input for an input component
     */
    private processGamepadInput(inputComponent: InputComponent): void {
        if (!this.inputManager?.gamepadState) {return;}
        
        const gamepadState = this.inputManager.gamepadState;
        
        // Process gamepad buttons
        if (gamepadState.buttons) {
            gamepadState.buttons.forEach((button: any, index: number) => {
                if (button.pressed) {
                    // Map gamepad button to input action
                    // This could be expanded based on gamepad button mappings
                    switch (index) {
                    case 0: // A button
                        inputComponent.handleKeyDown('Space'); // Map to shoot
                        break;
                    case 1: // B button
                        inputComponent.handleKeyDown('KeyE'); // Map to interact
                        break;
                    }
                }
            });
        }
        
        // Process analog sticks as axes
        if (gamepadState.leftStick) {
            // Map left stick to movement axes
            inputComponent.setAxisValue('horizontal', gamepadState.leftStick.x);
            inputComponent.setAxisValue('vertical', -gamepadState.leftStick.y); // Invert Y
        }
        
        if (gamepadState.rightStick) {
            // Map right stick to look axes
            inputComponent.setAxisValue('lookHorizontal', gamepadState.rightStick.x);
            inputComponent.setAxisValue('lookVertical', -gamepadState.rightStick.y); // Invert Y
        }
    }
    
    /**
     * Update previous input states for next frame
     */
    private updatePreviousStates(): void {
        if (!this.inputManager) {return;}
        
        // Update previous key states
        this.previousKeyStates.clear();
        for (const [key, state] of this.inputManager.keys) {
            this.previousKeyStates.set(key, state.pressed || false);
        }
        
        // Update previous mouse button states
        this.previousMouseStates.clear();
        for (const [button, state] of this.inputManager.mouseButtons) {
            const buttonNumber = parseInt(button.replace('Mouse', ''));
            this.previousMouseStates.set(buttonNumber, state.pressed || false);
        }
        
        // Update previous mouse position
        this.previousMousePosition = { ...this.inputManager.mousePosition };
    }
    
    /**
     * Get input manager reference
     */
    public getInputManager(): any {
        return this.inputManager;
    }
    
    /**
     * Set input manager reference (for testing or dependency injection)
     */
    public setInputManager(inputManager: any): void {
        this.inputManager = inputManager;
    }
    
    /**
     * Cleanup input system
     */
    public override cleanup(): void {
        this.previousKeyStates.clear();
        this.previousMouseStates.clear();
        this.inputManager = null;
    }
}
