/**
 * PhysicsSystem - Handles physics simulation and collision detection
 * Manages physics bodies, forces, and collision responses
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2 } from '@/types';
import { PhysicsComponent } from '@/components/PhysicsComponent';
import { TransformComponent } from '@/components/TransformComponent';
import { HealthComponent } from '@/components/HealthComponent';

interface CollisionPair {
    entityA: IGameEntity;
    entityB: IGameEntity;
    physicsA: PhysicsComponent;
    physicsB: PhysicsComponent;
}

export class PhysicsSystem extends BaseSystem {
    public static readonly NAME = 'PhysicsSystem';
    public static readonly PRIORITY = 1; // High priority - run early
    
    // Physics world settings
    public gravity: Vector2 = { x: 0, y: -9.81 };
    public timeStep: number = 1 / 60; // Fixed timestep for physics
    public maxSubSteps: number = 3;
    
    // Collision detection optimization
    private spatialGrid: Map<string, IGameEntity[]> = new Map();
    private gridSize: number = 4; // Size of each grid cell
    private worldBounds = { minX: -50, maxX: 50, minY: -50, maxY: 50 };
    
    // Collision tracking
    private activeCollisions: Set<string> = new Set();
    private previousCollisions: Set<string> = new Set();
    
    // Performance metrics
    private lastFrameCollisionChecks: number = 0;
    private lastFrameActiveCollisions: number = 0;
    
    constructor() {
        super(PhysicsSystem.NAME, PhysicsSystem.PRIORITY, ['physics', 'transform']);
    }
    
    /**
     * Initialize the physics system
     */
    public override async initialize(): Promise<void> {
        console.log('PhysicsSystem initialized');
    }
    
    /**
     * Update physics simulation
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        const startTime = performance.now();
        
        // Get all physics entities
        const physicsEntities = this.filterEntities(entities);
        
        // Clear spatial grid
        this.clearSpatialGrid();
        
        // Update physics components and populate spatial grid
        for (const entity of physicsEntities) {
            const physics = entity.getComponent<PhysicsComponent>('physics');
            const transform = entity.getComponent<TransformComponent>('transform');
            
            if (physics && transform) {
                // Apply gravity if enabled
                if (physics.useGravity && !physics.isStatic) {
                    physics.addForce({
                        x: this.gravity.x * physics.gravityScale * physics.mass,
                        y: this.gravity.y * physics.gravityScale * physics.mass
                    });
                }
                
                // Update physics component
                physics.update(deltaTime);
                
                // Add to spatial grid for collision detection
                this.addToSpatialGrid(entity);
            }
        }
        
        // Detect and resolve collisions
        this.detectCollisions(physicsEntities);
        this.resolveCollisions(deltaTime);
        
        // Update collision tracking
        this.updateCollisionTracking();
        
        this.recordUpdateTime(startTime);
    }
    
    /**
     * Add force to a specific entity
     */
    public addForce(entityId: string, force: Vector2, entities: IGameEntity[]): void {
        const entity = entities.find(e => e.id === entityId);
        if (!entity) {return;}
        
        const physics = entity.getComponent<PhysicsComponent>('physics');
        if (physics) {
            physics.addForce(force);
        }
    }
    
    /**
     * Add impulse to a specific entity
     */
    public addImpulse(entityId: string, impulse: Vector2, entities: IGameEntity[]): void {
        const entity = entities.find(e => e.id === entityId);
        if (!entity) {return;}
        
        const physics = entity.getComponent<PhysicsComponent>('physics');
        if (physics) {
            physics.addImpulse(impulse);
        }
    }
    
    /**
     * Perform raycast in the physics world
     */
    public raycast(from: Vector2, to: Vector2, entities: IGameEntity[]): { 
        hit: boolean; 
        entity?: IGameEntity; 
        point?: Vector2; 
        distance?: number; 
    } {
        const physicsEntities = entities.filter(entity => 
            entity.hasComponent('physics') && entity.hasComponent('transform')
        );
        
        let closestHit: { entity: IGameEntity; point: Vector2; distance: number } | null = null;
        
        for (const entity of physicsEntities) {
            const physics = entity.getComponent<PhysicsComponent>('physics');
            const transform = entity.getComponent<TransformComponent>('transform');
            
            if (!physics || !transform || physics.isTrigger) {continue;}
            
            const hit = this.raycastEntity(from, to, entity);
            if (hit && (!closestHit || hit.distance < closestHit.distance)) {
                closestHit = hit;
            }
        }
        
        if (closestHit) {
            return {
                hit: true,
                entity: closestHit.entity,
                point: closestHit.point,
                distance: closestHit.distance
            };
        }
        
        return { hit: false };
    }
    
    /**
     * Get all entities in a radius
     */
    public getEntitiesInRadius(center: Vector2, radius: number, entities: IGameEntity[]): IGameEntity[] {
        const result: IGameEntity[] = [];
        const radiusSquared = radius * radius;
        
        for (const entity of entities) {
            if (!entity.hasComponent('transform')) {continue;}
            
            const transform = entity.getComponent<TransformComponent>('transform');
            if (!transform) {continue;}
            
            const pos = transform.getWorldPosition();
            const distanceSquared = (pos.x - center.x) ** 2 + (pos.y - center.y) ** 2;
            
            if (distanceSquared <= radiusSquared) {
                result.push(entity);
            }
        }
        
        return result;
    }
    
    /**
     * Check if a point is inside any physics entity
     */
    public pointIntersect(point: Vector2, entities: IGameEntity[]): IGameEntity | null {
        for (const entity of entities) {
            const physics = entity.getComponent<PhysicsComponent>('physics');
            const transform = entity.getComponent<TransformComponent>('transform');
            
            if (!physics || !transform) {continue;}
            
            if (this.pointIntersectsEntity(point, entity)) {
                return entity;
            }
        }
        
        return null;
    }
    
    /**
     * Clear spatial grid
     */
    private clearSpatialGrid(): void {
        this.spatialGrid.clear();
    }
    
    /**
     * Add entity to spatial grid
     */
    private addToSpatialGrid(entity: IGameEntity): void {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        if (!physics) {return;}
        
        const bounds = physics.getBounds();
        
        // Calculate grid coordinates
        const minGridX = Math.floor(bounds.min.x / this.gridSize);
        const maxGridX = Math.floor(bounds.max.x / this.gridSize);
        const minGridY = Math.floor(bounds.min.y / this.gridSize);
        const maxGridY = Math.floor(bounds.max.y / this.gridSize);
        
        // Add entity to all grid cells it overlaps
        for (let x = minGridX; x <= maxGridX; x++) {
            for (let y = minGridY; y <= maxGridY; y++) {
                const key = `${x},${y}`;
                if (!this.spatialGrid.has(key)) {
                    this.spatialGrid.set(key, []);
                }
                this.spatialGrid.get(key)!.push(entity);
            }
        }
    }
    
    /**
     * Detect collisions using spatial grid
     */
    private detectCollisions(entities: IGameEntity[]): void {
        this.lastFrameCollisionChecks = 0;
        const collisionPairs: CollisionPair[] = [];
        const checkedPairs = new Set<string>();
        
        // Check collisions within each grid cell
        for (const cellEntities of this.spatialGrid.values()) {
            for (let i = 0; i < cellEntities.length; i++) {
                for (let j = i + 1; j < cellEntities.length; j++) {
                    const entityA = cellEntities[i];
                    const entityB = cellEntities[j];
                    
                    // Create unique pair key
                    const pairKey = entityA.id < entityB.id 
                        ? `${entityA.id}-${entityB.id}` 
                        : `${entityB.id}-${entityA.id}`;
                    
                    if (checkedPairs.has(pairKey)) {continue;}
                    checkedPairs.add(pairKey);
                    
                    const physicsA = entityA.getComponent<PhysicsComponent>('physics');
                    const physicsB = entityB.getComponent<PhysicsComponent>('physics');
                    
                    if (!physicsA || !physicsB) {continue;}
                    
                    // Check collision layers
                    if ((physicsA.collisionLayer & physicsB.collisionMask) === 0 &&
                        (physicsB.collisionLayer & physicsA.collisionMask) === 0) {
                        continue;
                    }
                    
                    this.lastFrameCollisionChecks++;
                    
                    // Check for overlap
                    if (physicsA.overlaps(physicsB)) {
                        collisionPairs.push({ entityA, entityB, physicsA, physicsB });
                        
                        const collisionKey = entityA.id < entityB.id 
                            ? `${entityA.id}-${entityB.id}` 
                            : `${entityB.id}-${entityA.id}`;
                        this.activeCollisions.add(collisionKey);
                    }
                }
            }
        }
        
        // Handle collision events
        for (const pair of collisionPairs) {
            pair.physicsA.handleCollision(pair.physicsB);
            pair.physicsB.handleCollision(pair.physicsA);
        }
        
        this.lastFrameActiveCollisions = collisionPairs.length;
    }
    
    /**
     * Resolve collision physics
     */
    private resolveCollisions(deltaTime: number): void {
        // Collision resolution is handled within PhysicsComponent.handleCollision
        // This method could be extended for more complex resolution algorithms
    }
    
    /**
     * Update collision tracking for enter/exit events
     */
    private updateCollisionTracking(): void {
        // Find collisions that ended this frame
        for (const collisionKey of this.previousCollisions) {
            if (!this.activeCollisions.has(collisionKey)) {
                // Collision ended
                const [entityAId, entityBId] = collisionKey.split('-');
                
                // Find entities and trigger exit events
                // This would need access to entity manager to look up entities by ID
                // For now, we rely on PhysicsComponent to handle this internally
            }
        }
        
        // Update previous collisions
        this.previousCollisions.clear();
        for (const collisionKey of this.activeCollisions) {
            this.previousCollisions.add(collisionKey);
        }
        this.activeCollisions.clear();
    }
    
    /**
     * Raycast against a specific entity
     */
    private raycastEntity(from: Vector2, to: Vector2, entity: IGameEntity): { 
        entity: IGameEntity; 
        point: Vector2; 
        distance: number; 
    } | null {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        const transform = entity.getComponent<TransformComponent>('transform');
        
        if (!physics || !transform) {return null;}
        
        const pos = transform.getWorldPosition();
        const shape = physics.collisionShape;
        
        // Simple circle raycast for now
        if (shape.type === 'circle') {
            const radius = shape.radius || 0.5;
            
            // Vector from ray start to circle center
            const toCenter = { x: pos.x - from.x, y: pos.y - from.y };
            
            // Ray direction
            const rayDir = { x: to.x - from.x, y: to.y - from.y };
            const rayLength = Math.sqrt(rayDir.x * rayDir.x + rayDir.y * rayDir.y);
            rayDir.x /= rayLength;
            rayDir.y /= rayLength;
            
            // Project center onto ray
            const projection = toCenter.x * rayDir.x + toCenter.y * rayDir.y;
            
            // Closest point on ray to circle center
            const closestPoint = {
                x: from.x + rayDir.x * Math.max(0, Math.min(rayLength, projection)),
                y: from.y + rayDir.y * Math.max(0, Math.min(rayLength, projection))
            };
            
            // Distance from closest point to circle center
            const distance = Math.sqrt(
                (closestPoint.x - pos.x) ** 2 + (closestPoint.y - pos.y) ** 2
            );
            
            if (distance <= radius) {
                return {
                    entity,
                    point: closestPoint,
                    distance: Math.sqrt((closestPoint.x - from.x) ** 2 + (closestPoint.y - from.y) ** 2)
                };
            }
        }
        
        return null;
    }
    
    /**
     * Check if a point intersects with an entity
     */
    private pointIntersectsEntity(point: Vector2, entity: IGameEntity): boolean {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        const transform = entity.getComponent<TransformComponent>('transform');
        
        if (!physics || !transform) {return false;}
        
        const pos = transform.getWorldPosition();
        const shape = physics.collisionShape;
        
        switch (shape.type) {
        case 'circle':
            const radius = shape.radius || 0.5;
            const distance = Math.sqrt((point.x - pos.x) ** 2 + (point.y - pos.y) ** 2);
            return distance <= radius;
                
        case 'rectangle':
            const halfWidth = (shape.width || 1) / 2;
            const halfHeight = (shape.height || 1) / 2;
            return point.x >= pos.x - halfWidth &&
                       point.x <= pos.x + halfWidth &&
                       point.y >= pos.y - halfHeight &&
                       point.y <= pos.y + halfHeight;
                       
        default:
            return false;
        }
    }
    
    /**
     * Get physics system statistics
     */
    public getStats(): {
        activeEntities: number;
        collisionChecks: number;
        activeCollisions: number;
        gridCells: number;
        averageUpdateTime: number;
        } {
        return {
            activeEntities: this.filterEntities([]).length,
            collisionChecks: this.lastFrameCollisionChecks,
            activeCollisions: this.lastFrameActiveCollisions,
            gridCells: this.spatialGrid.size,
            averageUpdateTime: this.getAverageUpdateTime()
        };
    }
    
    /**
     * Cleanup system resources
     */
    public override cleanup(): void {
        this.spatialGrid.clear();
        this.activeCollisions.clear();
        this.previousCollisions.clear();
    }
}
