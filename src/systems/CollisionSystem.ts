/**
 * CollisionSystem - <PERSON>les collision responses and callbacks
 * Works in conjunction with PhysicsSystem to provide gameplay collision logic
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2 } from '@/types';
import { PhysicsComponent } from '@/components/PhysicsComponent';
import { TransformComponent } from '@/components/TransformComponent';
import { HealthComponent } from '@/components/HealthComponent';

interface CollisionEvent {
    entityA: IGameEntity;
    entityB: IGameEntity;
    contactPoint: Vector2;
    collisionNormal: Vector2;
    penetrationDepth: number;
    timestamp: number;
}

interface CollisionRule {
    entityTypeA: string;
    entityTypeB: string;
    handler: (entityA: IGameEntity, entityB: IGameEntity, event: CollisionEvent) => void;
    priority: number;
}

export class CollisionSystem extends BaseSystem {
    public static readonly NAME = 'CollisionSystem';
    public static readonly PRIORITY = 3; // After movement, physics handles collision detection
    
    // Collision rules registry
    private collisionRules: CollisionRule[] = [];
    
    // Recent collision events for processing
    private collisionEvents: CollisionEvent[] = [];
    
    // Collision callbacks
    private collisionCallbacks: Map<string, (event: CollisionEvent) => void> = new Map();
    
    constructor() {
        super(CollisionSystem.NAME, CollisionSystem.PRIORITY, ['physics', 'transform']);
        this.setupDefaultCollisionRules();
    }
    
    /**
     * Initialize the collision system
     */
    public override async initialize(): Promise<void> {
        console.log('CollisionSystem initialized');
    }
    
    /**
     * Update collision system - process collision events and responses
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        // Clear previous frame's events
        this.collisionEvents = [];
        
        // Collect collision events from physics components
        this.collectCollisionEvents(entities);
        
        // Process collision events
        this.processCollisionEvents();
        
        // Apply collision responses
        this.applyCollisionResponses(deltaTime);
    }
    
    /**
     * Collect collision events from physics components
     */
    private collectCollisionEvents(entities: IGameEntity[]): void {
        const physicsEntities = this.filterEntities(entities);
        
        for (const entity of physicsEntities) {
            const physics = entity.getComponent<PhysicsComponent>('physics');
            const transform = entity.getComponent<TransformComponent>('transform');
            
            if (!physics || !transform) {continue;}
            
            // Check for collision events in physics component
            const collisions = physics.getCollisionEvents();
            
            for (const collision of collisions) {
                // Create collision event
                const collisionEvent: CollisionEvent = {
                    entityA: entity,
                    entityB: collision.otherEntity,
                    contactPoint: collision.contactPoint,
                    collisionNormal: collision.normal,
                    penetrationDepth: collision.penetration,
                    timestamp: performance.now()
                };
                
                this.collisionEvents.push(collisionEvent);
            }
        }
    }
    
    /**
     * Process collision events according to rules
     */
    private processCollisionEvents(): void {
        for (const event of this.collisionEvents) {
            // Find applicable collision rules
            const applicableRules = this.findApplicableRules(event);
            
            // Sort by priority and execute
            applicableRules.sort((a, b) => a.priority - b.priority);
            
            for (const rule of applicableRules) {
                try {
                    rule.handler(event.entityA, event.entityB, event);
                } catch (error) {
                    console.error('Error executing collision rule:', error);
                }
            }
            
            // Execute global collision callbacks
            this.executeCollisionCallbacks(event);
        }
    }
    
    /**
     * Find collision rules applicable to the collision event
     */
    private findApplicableRules(event: CollisionEvent): CollisionRule[] {
        const typeA = event.entityA.type;
        const typeB = event.entityB.type;
        
        return this.collisionRules.filter(rule => 
            (rule.entityTypeA === typeA && rule.entityTypeB === typeB) ||
            (rule.entityTypeA === typeB && rule.entityTypeB === typeA) ||
            rule.entityTypeA === '*' || rule.entityTypeB === '*'
        );
    }
    
    /**
     * Execute global collision callbacks
     */
    private executeCollisionCallbacks(event: CollisionEvent): void {
        // Entity-specific callbacks
        const callbackA = this.collisionCallbacks.get(event.entityA.id);
        if (callbackA) {
            callbackA(event);
        }
        
        const callbackB = this.collisionCallbacks.get(event.entityB.id);
        if (callbackB) {
            callbackB(event);
        }
        
        // Global callback
        const globalCallback = this.collisionCallbacks.get('*');
        if (globalCallback) {
            globalCallback(event);
        }
    }
    
    /**
     * Apply collision responses (damage, destruction, etc.)
     */
    private applyCollisionResponses(deltaTime: number): void {
        // Additional processing that might need to happen after all collision rules
        // This could include delayed effects, cleanup, etc.
    }
    
    /**
     * Setup default collision rules for common game interactions
     */
    private setupDefaultCollisionRules(): void {
        // Player vs Enemy
        this.addCollisionRule('player', 'enemy', (playerEntity, enemyEntity, event) => {
            this.handlePlayerEnemyCollision(playerEntity, enemyEntity, event);
        }, 1);
        
        // Bullet vs Player
        this.addCollisionRule('bullet', 'player', (bulletEntity, playerEntity, event) => {
            this.handleBulletPlayerCollision(bulletEntity, playerEntity, event);
        }, 1);
        
        // Bullet vs Enemy
        this.addCollisionRule('bullet', 'enemy', (bulletEntity, enemyEntity, event) => {
            this.handleBulletEnemyCollision(bulletEntity, enemyEntity, event);
        }, 1);
        
        // Player vs PowerUp
        this.addCollisionRule('player', 'powerup', (playerEntity, powerUpEntity, event) => {
            this.handlePlayerPowerUpCollision(playerEntity, powerUpEntity, event);
        }, 1);
        
        // Bullet vs Obstacle
        this.addCollisionRule('bullet', 'obstacle', (bulletEntity, obstacleEntity, event) => {
            this.handleBulletObstacleCollision(bulletEntity, obstacleEntity, event);
        }, 1);
        
        // Generic destruction rule for any entity vs bullet
        this.addCollisionRule('*', 'bullet', (entity, bulletEntity, event) => {
            this.handleGenericBulletCollision(entity, bulletEntity, event);
        }, 10); // Low priority
    }
    
    /**
     * Handle player-enemy collision
     */
    private handlePlayerEnemyCollision(playerEntity: IGameEntity, enemyEntity: IGameEntity, event: CollisionEvent): void {
        const playerHealth = playerEntity.getComponent<HealthComponent>('health');
        
        if (playerHealth) {
            // Player takes damage from enemy contact
            const damage = 10; // Could be configurable based on enemy type
            playerHealth.takeDamage(damage);
            
            console.log(`Player took ${damage} damage from enemy collision`);
            
            // Apply knockback to player
            this.applyKnockback(playerEntity, event.collisionNormal, 5.0);
        }
    }
    
    /**
     * Handle bullet-player collision
     */
    private handleBulletPlayerCollision(bulletEntity: IGameEntity, playerEntity: IGameEntity, event: CollisionEvent): void {
        // Check if bullet is from enemy (friendly fire prevention)
        if (this.isBulletFromEnemy(bulletEntity)) {
            const playerHealth = playerEntity.getComponent<HealthComponent>('health');
            
            if (playerHealth) {
                const damage = this.getBulletDamage(bulletEntity);
                playerHealth.takeDamage(damage);
                
                console.log(`Player took ${damage} damage from bullet`);
                
                // Apply knockback
                this.applyKnockback(playerEntity, event.collisionNormal, 2.0);
            }
            
            // Destroy bullet
            this.destroyEntity(bulletEntity);
        }
    }
    
    /**
     * Handle bullet-enemy collision
     */
    private handleBulletEnemyCollision(bulletEntity: IGameEntity, enemyEntity: IGameEntity, event: CollisionEvent): void {
        // Check if bullet is from player
        if (this.isBulletFromPlayer(bulletEntity)) {
            const enemyHealth = enemyEntity.getComponent<HealthComponent>('health');
            
            if (enemyHealth) {
                const damage = this.getBulletDamage(bulletEntity);
                enemyHealth.takeDamage(damage);
                
                console.log(`Enemy took ${damage} damage from bullet`);
                
                // Apply knockback
                this.applyKnockback(enemyEntity, event.collisionNormal, 3.0);
            }
            
            // Destroy bullet
            this.destroyEntity(bulletEntity);
        }
    }
    
    /**
     * Handle player-powerup collision
     */
    private handlePlayerPowerUpCollision(playerEntity: IGameEntity, powerUpEntity: IGameEntity, event: CollisionEvent): void {
        // Apply power-up effect to player
        this.applyPowerUpEffect(playerEntity, powerUpEntity);
        
        // Destroy power-up
        this.destroyEntity(powerUpEntity);
        
        console.log('Player collected power-up');
    }
    
    /**
     * Handle bullet-obstacle collision
     */
    private handleBulletObstacleCollision(bulletEntity: IGameEntity, obstacleEntity: IGameEntity, event: CollisionEvent): void {
        // Bullets are destroyed by obstacles
        this.destroyEntity(bulletEntity);
        
        console.log('Bullet hit obstacle');
    }
    
    /**
     * Handle generic bullet collision
     */
    private handleGenericBulletCollision(entity: IGameEntity, bulletEntity: IGameEntity, event: CollisionEvent): void {
        // Generic bullet collision - only destroy bullet if no other rule handled it
        if (!this.wasCollisionHandled(event)) {
            this.destroyEntity(bulletEntity);
        }
    }
    
    /**
     * Apply knockback force to entity
     */
    private applyKnockback(entity: IGameEntity, direction: Vector2, force: number): void {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        
        if (physics && !physics.isStatic) {
            const knockbackForce = {
                x: direction.x * force,
                y: direction.y * force
            };
            
            physics.addImpulse(knockbackForce);
        }
    }
    
    /**
     * Check if bullet is from enemy
     */
    private isBulletFromEnemy(bulletEntity: IGameEntity): boolean {
        // This would check bullet's owner/source
        // For now, simple implementation based on entity data
        return (bulletEntity as any).source === 'enemy';
    }
    
    /**
     * Check if bullet is from player
     */
    private isBulletFromPlayer(bulletEntity: IGameEntity): boolean {
        return (bulletEntity as any).source === 'player';
    }
    
    /**
     * Get bullet damage amount
     */
    private getBulletDamage(bulletEntity: IGameEntity): number {
        // Default damage, could be stored in bullet component
        return (bulletEntity as any).damage || 20;
    }
    
    /**
     * Apply power-up effect to player
     */
    private applyPowerUpEffect(playerEntity: IGameEntity, powerUpEntity: IGameEntity): void {
        const powerUpType = (powerUpEntity as any).powerUpType || 'health';
        
        switch (powerUpType) {
        case 'health': {
            const health = playerEntity.getComponent<HealthComponent>('health');
            if (health) {
                health.heal(25);
            }
            break;
        }
        case 'speed': {
            // Temporarily increase movement speed
            // This would need integration with MovementSystem
            break;
        }
        case 'weapon': {
            // Upgrade weapon
            // This would need integration with WeaponSystem
            break;
        }
        }
    }
    
    /**
     * Destroy entity (mark for removal)
     */
    private destroyEntity(entity: IGameEntity): void {
        entity.active = false;
        entity.destroy();
    }
    
    /**
     * Check if collision was already handled by a higher priority rule
     */
    private wasCollisionHandled(event: CollisionEvent): boolean {
        // Simple implementation - could be more sophisticated
        return false;
    }
    
    /**
     * Add collision rule
     */
    public addCollisionRule(
        entityTypeA: string,
        entityTypeB: string,
        handler: (entityA: IGameEntity, entityB: IGameEntity, event: CollisionEvent) => void,
        priority: number = 5
    ): void {
        this.collisionRules.push({
            entityTypeA,
            entityTypeB,
            handler,
            priority
        });
        
        // Sort rules by priority
        this.collisionRules.sort((a, b) => a.priority - b.priority);
    }
    
    /**
     * Remove collision rule
     */
    public removeCollisionRule(entityTypeA: string, entityTypeB: string): void {
        this.collisionRules = this.collisionRules.filter(rule =>
            !(rule.entityTypeA === entityTypeA && rule.entityTypeB === entityTypeB) &&
            !(rule.entityTypeA === entityTypeB && rule.entityTypeB === entityTypeA)
        );
    }
    
    /**
     * Add collision callback for specific entity
     */
    public addCollisionCallback(entityId: string, callback: (event: CollisionEvent) => void): void {
        this.collisionCallbacks.set(entityId, callback);
    }
    
    /**
     * Remove collision callback
     */
    public removeCollisionCallback(entityId: string): void {
        this.collisionCallbacks.delete(entityId);
    }
    
    /**
     * Add global collision callback
     */
    public addGlobalCollisionCallback(callback: (event: CollisionEvent) => void): void {
        this.collisionCallbacks.set('*', callback);
    }
    
    /**
     * Get collision events from current frame
     */
    public getCollisionEvents(): CollisionEvent[] {
        return [...this.collisionEvents];
    }
    
    /**
     * Cleanup collision system
     */
    public override cleanup(): void {
        this.collisionRules = [];
        this.collisionEvents = [];
        this.collisionCallbacks.clear();
    }
}
