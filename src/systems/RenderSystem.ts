/**
 * RenderSystem - Handles rendering of entities with mesh components
 * Manages the visual representation of game entities in the 3D scene
 */

import * as THREE from 'three';
import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2 } from '@/types';
import { TransformComponent } from '@/components/TransformComponent';
import { MeshComponent } from '@/components/MeshComponent';
import { HealthComponent } from '@/components/HealthComponent';

interface RenderStats {
    visibleEntities: number;
    culledEntities: number;
    drawCalls: number;
    triangles: number;
}

export class RenderSystem extends BaseSystem {
    public static readonly NAME = 'RenderSystem';
    public static readonly PRIORITY = 10; // Low priority - render last
    
    private renderer: any; // THREE.WebGLRenderer
    private scene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    
    // Render optimization
    private frustum: THREE.Frustum = new THREE.Frustum();
    private cameraMatrix: THREE.Matrix4 = new THREE.Matrix4();
    
    // Render stats
    private stats: RenderStats = {
        visibleEntities: 0,
        culledEntities: 0,
        drawCalls: 0,
        triangles: 0
    };
    
    // Entity mesh cache
    private meshCache: Map<string, THREE.Object3D> = new Map();
    
    // Material cache for performance
    private materialCache: Map<string, THREE.Material> = new Map();
    
    constructor(renderer: any, camera: THREE.PerspectiveCamera) {
        super(RenderSystem.NAME, RenderSystem.PRIORITY, ['transform', 'mesh']);
        this.renderer = renderer;
        this.camera = camera;
        this.scene = new THREE.Scene();
    }
    
    /**
     * Initialize the render system
     */
    public override async initialize(): Promise<void> {
        // Set up basic scene lighting
        this.setupLighting();
        
        // Configure renderer settings
        this.configureRenderer();
        
        console.log('RenderSystem initialized');
    }
    
    /**
     * Update render system - update entity transforms and render scene
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        // Reset stats
        this.resetStats();
        
        // Update camera matrix for frustum culling
        this.updateFrustum();
        
        // Process all renderable entities
        const renderableEntities = this.filterEntities(entities);
        
        for (const entity of renderableEntities) {
            this.updateEntityMesh(entity);
        }
        
        // Clean up removed entities
        this.cleanupRemovedEntities(renderableEntities);
        
        // Update any visual effects
        this.updateVisualEffects(deltaTime);
        
        // Render the scene
        this.renderScene();
    }
    
    /**
     * Update entity mesh representation
     */
    private updateEntityMesh(entity: IGameEntity): void {
        const transform = entity.getComponent<TransformComponent>('transform');
        const meshComponent = entity.getComponent<MeshComponent>('mesh');
        
        if (!transform || !meshComponent) {return;}
        
        // Get or create mesh object
        let meshObject = this.meshCache.get(entity.id);
        
        if (!meshObject) {
            meshObject = this.createMeshObject(meshComponent) || undefined;
            if (meshObject) {
                this.meshCache.set(entity.id, meshObject);
                this.scene.add(meshObject);
            }
        }
        
        if (!meshObject) {return;}
        
        // Update transform
        this.updateMeshTransform(meshObject, transform);
        
        // Update material properties
        this.updateMeshMaterial(meshObject, entity, meshComponent);
        
        // Frustum culling
        if (this.isObjectVisible(meshObject)) {
            meshObject.visible = true;
            this.stats.visibleEntities++;
        } else {
            meshObject.visible = false;
            this.stats.culledEntities++;
        }
    }
    
    /**
     * Create mesh object from mesh component
     */
    private createMeshObject(meshComponent: MeshComponent): THREE.Object3D | null {
        try {
            switch (meshComponent.primitiveType) {
            case 'box':
                return this.createCube(meshComponent);
            case 'sphere':
                return this.createSphere(meshComponent);
            case 'plane':
                return this.createPlane(meshComponent);
            case 'cylinder':
                return this.createCylinder(meshComponent);
            default:
                console.warn(`Unknown primitive type: ${meshComponent.primitiveType}`);
                return this.createCube(meshComponent); // Default to cube
            }
        } catch (error) {
            console.error('Error creating mesh object:', error);
            return null;
        }
    }
    
    /**
     * Create cube mesh
     */
    private createCube(meshComponent: MeshComponent): THREE.Mesh {
        const geometry = new THREE.BoxGeometry(
            meshComponent.size.x,
            meshComponent.size.y,
            meshComponent.size.z || 1.0
        );
        
        const material = this.getMaterial(meshComponent);
        return new THREE.Mesh(geometry, material);
    }
    
    /**
     * Create sphere mesh
     */
    private createSphere(meshComponent: MeshComponent): THREE.Mesh {
        const radius = Math.max(meshComponent.size.x, meshComponent.size.y) * 0.5;
        const geometry = new THREE.SphereGeometry(radius, 16, 16);
        
        const material = this.getMaterial(meshComponent);
        return new THREE.Mesh(geometry, material);
    }
    
    /**
     * Create plane mesh
     */
    private createPlane(meshComponent: MeshComponent): THREE.Mesh {
        const geometry = new THREE.PlaneGeometry(
            meshComponent.size.x,
            meshComponent.size.y
        );
        
        const material = this.getMaterial(meshComponent);
        return new THREE.Mesh(geometry, material);
    }
    
    /**
     * Create cylinder mesh
     */
    private createCylinder(meshComponent: MeshComponent): THREE.Mesh {
        const radius = Math.max(meshComponent.size.x, meshComponent.size.y) * 0.5;
        const height = meshComponent.size.z || 1.0;
        const geometry = new THREE.CylinderGeometry(radius, radius, height, 16);
        
        const material = this.getMaterial(meshComponent);
        return new THREE.Mesh(geometry, material);
    }
    

    
    /**
     * Get or create material for mesh component
     */
    private getMaterial(meshComponent: MeshComponent): THREE.Material {
        const materialKey = this.getMaterialKey(meshComponent);
        
        let material = this.materialCache.get(materialKey);
        if (!material) {
            material = this.createMaterial(meshComponent);
            this.materialCache.set(materialKey, material);
        }
        
        return material;
    }
    
    /**
     * Create material key for caching
     */
    private getMaterialKey(meshComponent: MeshComponent): string {
        return `${meshComponent.materialType}_${meshComponent.color.r}_${meshComponent.color.g}_${meshComponent.color.b}_${meshComponent.color.a}`;
    }
    
    /**
     * Create material from mesh component
     */
    private createMaterial(meshComponent: MeshComponent): THREE.Material {
        const color = new THREE.Color(
            meshComponent.color.r,
            meshComponent.color.g,
            meshComponent.color.b
        );
        
        switch (meshComponent.materialType) {
        case 'basic':
            return new THREE.MeshBasicMaterial({
                color,
                transparent: meshComponent.color.a < 1.0,
                opacity: meshComponent.color.a
            });
            
        case 'lambert':
            return new THREE.MeshLambertMaterial({
                color,
                transparent: meshComponent.color.a < 1.0,
                opacity: meshComponent.color.a
            });
            
        case 'phong':
            return new THREE.MeshPhongMaterial({
                color,
                transparent: meshComponent.color.a < 1.0,
                opacity: meshComponent.color.a,
                shininess: 30
            });
            
        case 'standard':
            return new THREE.MeshStandardMaterial({
                color,
                transparent: meshComponent.color.a < 1.0,
                opacity: meshComponent.color.a,
                metalness: 0.1,
                roughness: 0.8
            });
            
        default:
            return new THREE.MeshBasicMaterial({
                color,
                transparent: meshComponent.color.a < 1.0,
                opacity: meshComponent.color.a
            });
        }
    }
    
    /**
     * Update mesh transform from transform component
     */
    private updateMeshTransform(meshObject: THREE.Object3D, transform: TransformComponent): void {
        // Update position (convert 2D to 3D)
        meshObject.position.set(
            transform.position.x,
            transform.position.y,
            0 // Z is always 0 for 2.5D
        );

        // Update rotation (rotation is a single number in 2.5D)
        meshObject.rotation.set(
            0, // X rotation
            0, // Y rotation
            transform.rotation // Z rotation (main rotation in 2.5D)
        );

        // Update scale (convert 2D to 3D)
        meshObject.scale.set(
            transform.scale.x,
            transform.scale.y,
            1 // Z scale is always 1 for 2.5D
        );
    }
    
    /**
     * Update mesh material properties based on entity state
     */
    private updateMeshMaterial(meshObject: THREE.Object3D, entity: IGameEntity, meshComponent: MeshComponent): void {
        const health = entity.getComponent<HealthComponent>('health');
        
        if (health && meshObject instanceof THREE.Mesh) {
            const material = meshObject.material as THREE.Material;
            
            // Visual feedback for damaged entities
            if (health.currentHealth < health.maxHealth * 0.5) {
                // Flash red when low health
                const flashIntensity = Math.sin(Date.now() * 0.01) * 0.5 + 0.5;
                if ('color' in material) {
                    (material as any).color.setRGB(
                        Math.min(1, meshComponent.color.r + flashIntensity * 0.5),
                        meshComponent.color.g * (1 - flashIntensity * 0.5),
                        meshComponent.color.b * (1 - flashIntensity * 0.5)
                    );
                }
            }
        }
    }
    
    /**
     * Check if object is visible in camera frustum
     */
    private isObjectVisible(object: THREE.Object3D): boolean {
        // Simple frustum culling
        if (object instanceof THREE.Mesh && object.geometry) {
            object.geometry.computeBoundingSphere();
            const boundingSphere = object.geometry.boundingSphere;
            
            if (boundingSphere) {
                const worldSphere = boundingSphere.clone();
                worldSphere.applyMatrix4(object.matrixWorld);
                return this.frustum.intersectsSphere(worldSphere);
            }
        }
        
        return true; // Default to visible if we can't determine
    }
    
    /**
     * Update camera frustum for culling
     */
    private updateFrustum(): void {
        this.cameraMatrix.multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse);
        this.frustum.setFromProjectionMatrix(this.cameraMatrix);
    }
    
    /**
     * Clean up removed entities
     */
    private cleanupRemovedEntities(activeEntities: IGameEntity[]): void {
        const activeIds = new Set(activeEntities.map(e => e.id));
        
        for (const [entityId, meshObject] of this.meshCache) {
            if (!activeIds.has(entityId)) {
                // Remove from scene
                this.scene.remove(meshObject);
                
                // Clean up geometry and materials
                if (meshObject instanceof THREE.Mesh) {
                    meshObject.geometry.dispose();
                    if (Array.isArray(meshObject.material)) {
                        meshObject.material.forEach(mat => mat.dispose());
                    } else {
                        meshObject.material.dispose();
                    }
                }
                
                // Remove from cache
                this.meshCache.delete(entityId);
            }
        }
    }
    
    /**
     * Update visual effects
     */
    private updateVisualEffects(deltaTime: number): void {
        // Update any animated materials, particle effects, etc.
        // This would be expanded based on specific visual effects needed
    }
    
    /**
     * Setup basic scene lighting
     */
    private setupLighting(): void {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.setScalar(2048);
        this.scene.add(directionalLight);
        
        // Point light for dynamic lighting
        const pointLight = new THREE.PointLight(0xff6600, 0.5, 50);
        pointLight.position.set(0, 10, 0);
        this.scene.add(pointLight);
    }
    
    /**
     * Configure renderer settings
     */
    private configureRenderer(): void {
        if (this.renderer) {
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            this.renderer.setClearColor(0x202020);
        }
    }
    
    /**
     * Render the scene
     */
    private renderScene(): void {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
            
            // Update render stats
            this.stats.drawCalls = this.renderer.info.render.calls;
            this.stats.triangles = this.renderer.info.render.triangles;
        }
    }
    
    /**
     * Reset render stats
     */
    private resetStats(): void {
        this.stats.visibleEntities = 0;
        this.stats.culledEntities = 0;
        this.stats.drawCalls = 0;
        this.stats.triangles = 0;
    }
    
    /**
     * Get render statistics
     */
    public getStats(): RenderStats {
        return { ...this.stats };
    }
    
    /**
     * Get scene reference
     */
    public getScene(): THREE.Scene {
        return this.scene;
    }
    
    /**
     * Set camera reference
     */
    public setCamera(camera: THREE.PerspectiveCamera): void {
        this.camera = camera;
    }
    
    /**
     * Cleanup render system
     */
    public override cleanup(): void {
        // Clean up all cached meshes
        for (const [entityId, meshObject] of this.meshCache) {
            this.scene.remove(meshObject);
            
            if (meshObject instanceof THREE.Mesh) {
                meshObject.geometry.dispose();
                if (Array.isArray(meshObject.material)) {
                    meshObject.material.forEach(mat => mat.dispose());
                } else {
                    meshObject.material.dispose();
                }
            }
        }
        
        this.meshCache.clear();
        
        // Clean up materials
        for (const material of this.materialCache.values()) {
            material.dispose();
        }
        this.materialCache.clear();
        
        // Clear scene
        this.scene.clear();
    }
}
