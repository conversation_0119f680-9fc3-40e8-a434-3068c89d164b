/**
 * MovementSystem - Handles entity movement based on input and physics
 * Processes movement input and applies forces/velocities to physics bodies
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2 } from '@/types';
import { InputComponent } from '@/components/InputComponent';
import { PhysicsComponent } from '@/components/PhysicsComponent';
import { TransformComponent } from '@/components/TransformComponent';
import { AIComponent } from '@/components/AIComponent';

interface MovementConfig {
    readonly maxSpeed: number;
    readonly acceleration: number;
    readonly deceleration: number;
    readonly turnSpeed: number;
}

export class MovementSystem extends BaseSystem {
    public static readonly NAME = 'MovementSystem';
    public static readonly PRIORITY = 2; // After input, before physics
    
    // Default movement configuration
    private defaultConfig: MovementConfig = {
        maxSpeed: 10.0,
        acceleration: 20.0,
        deceleration: 15.0,
        turnSpeed: 5.0
    };
    
    // Entity-specific movement configurations
    private movementConfigs: Map<string, MovementConfig> = new Map();
    
    constructor() {
        super(MovementSystem.NAME, MovementSystem.PRIORITY, ['transform']);
    }
    
    /**
     * Initialize the movement system
     */
    public override async initialize(): Promise<void> {
        console.log('MovementSystem initialized');
    }
    
    /**
     * Update movement system - process movement for all entities
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        const movableEntities = this.filterEntities(entities);
        
        for (const entity of movableEntities) {
            const transform = entity.getComponent<TransformComponent>('transform');
            if (!transform) {continue;}
            
            // Process different types of movement
            if (entity.hasComponent('input')) {
                this.processPlayerMovement(entity, deltaTime);
            } else if (entity.hasComponent('ai')) {
                this.processAIMovement(entity, deltaTime);
            } else if (entity.hasComponent('physics')) {
                this.processPhysicsMovement(entity, deltaTime);
            }
        }
    }
    
    /**
     * Process player movement based on input
     */
    private processPlayerMovement(entity: IGameEntity, deltaTime: number): void {
        const input = entity.getComponent<InputComponent>('input');
        const physics = entity.getComponent<PhysicsComponent>('physics');
        const transform = entity.getComponent<TransformComponent>('transform');
        
        if (!input || !transform) {return;}
        
        const config = this.getMovementConfig(entity.id);
        const movementVector = input.getMovementVector();
        
        if (physics) {
            // Physics-based movement
            this.applyPhysicsMovement(entity, movementVector, config, deltaTime);
        } else {
            // Direct transform movement
            this.applyDirectMovement(entity, movementVector, config, deltaTime);
        }
        
        // Handle rotation/facing direction
        this.updateEntityFacing(entity, input, deltaTime);
    }
    
    /**
     * Process AI movement
     */
    private processAIMovement(entity: IGameEntity, deltaTime: number): void {
        const ai = entity.getComponent<AIComponent>('ai');
        const physics = entity.getComponent<PhysicsComponent>('physics');
        const transform = entity.getComponent<TransformComponent>('transform');
        
        if (!ai || !transform) {return;}
        
        const config = this.getMovementConfig(entity.id);
        
        // Get movement vector from AI
        const movementVector = ai.getMovementVector();
        
        if (physics) {
            // Physics-based movement
            this.applyPhysicsMovement(entity, movementVector, config, deltaTime);
        } else {
            // Direct transform movement
            this.applyDirectMovement(entity, movementVector, config, deltaTime);
        }
        
        // Update facing direction based on AI target
        this.updateAIFacing(entity, ai, deltaTime);
    }
    
    /**
     * Process physics-only movement (for projectiles, particles, etc.)
     */
    private processPhysicsMovement(entity: IGameEntity, deltaTime: number): void {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        if (!physics) {return;}
        
        // Physics component handles its own movement through velocity
        // This method can be used for additional movement logic like drag, etc.
        
        // Apply air resistance/drag
        if (physics.applyDrag) {
            const dragCoefficient = 0.98; // Adjust as needed
            physics.velocity = {
                x: physics.velocity.x * dragCoefficient,
                y: physics.velocity.y * dragCoefficient
            };
        }
    }
    
    /**
     * Apply physics-based movement using forces
     */
    private applyPhysicsMovement(
        entity: IGameEntity, 
        movementVector: Vector2, 
        config: MovementConfig, 
        deltaTime: number
    ): void {
        const physics = entity.getComponent<PhysicsComponent>('physics');
        if (!physics || physics.isStatic) {return;}
        
        // Calculate desired velocity
        const desiredVelocity = {
            x: movementVector.x * config.maxSpeed,
            y: movementVector.y * config.maxSpeed
        };
        
        // Apply acceleration/deceleration forces
        const currentVelocity = physics.velocity;
        const velocityDiff = {
            x: desiredVelocity.x - currentVelocity.x,
            y: desiredVelocity.y - currentVelocity.y
        };
        
        // Determine acceleration or deceleration
        const isAccelerating = Math.abs(desiredVelocity.x) > Math.abs(currentVelocity.x) || 
                              Math.abs(desiredVelocity.y) > Math.abs(currentVelocity.y);
        const rate = isAccelerating ? config.acceleration : config.deceleration;
        
        // Apply force
        const force = {
            x: velocityDiff.x * rate * physics.mass,
            y: velocityDiff.y * rate * physics.mass
        };
        
        physics.addForce(force);
    }
    
    /**
     * Apply direct transform movement (no physics)
     */
    private applyDirectMovement(
        entity: IGameEntity, 
        movementVector: Vector2, 
        config: MovementConfig, 
        deltaTime: number
    ): void {
        const transform = entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        // Calculate movement distance
        const moveDistance = {
            x: movementVector.x * config.maxSpeed * deltaTime,
            y: movementVector.y * config.maxSpeed * deltaTime
        };
        
        // Update position (2D movement)
        transform.setPosition(
            transform.position.x + moveDistance.x,
            transform.position.y + moveDistance.y
        );
    }
    
    /**
     * Update entity facing direction based on input
     */
    private updateEntityFacing(entity: IGameEntity, input: InputComponent, deltaTime: number): void {
        const transform = entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        const config = this.getMovementConfig(entity.id);
        
        // Get look direction from input (mouse position or look axes)
        const lookVector = input.getLookVector();
        
        if (lookVector.x !== 0 || lookVector.y !== 0) {
            // Calculate target rotation based on look direction
            const targetAngle = Math.atan2(lookVector.y, lookVector.x);
            const currentAngle = transform.rotation; // rotation is a number in 2.5D

            // Smoothly rotate towards target
            const angleDiff = this.normalizeAngle(targetAngle - currentAngle);
            const rotationSpeed = config.turnSpeed * deltaTime;

            let newAngle = currentAngle;
            if (Math.abs(angleDiff) > rotationSpeed) {
                newAngle += Math.sign(angleDiff) * rotationSpeed;
            } else {
                newAngle = targetAngle;
            }

            transform.setRotation(newAngle);
        }
    }
    
    /**
     * Update AI entity facing direction
     */
    private updateAIFacing(entity: IGameEntity, ai: AIComponent, deltaTime: number): void {
        const transform = entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        const config = this.getMovementConfig(entity.id);
        const target = ai.getTarget();
        
        if (target) {
            // Face towards target
            const direction = {
                x: target.x - transform.position.x,
                y: target.y - transform.position.y
            };
            
            const targetAngle = Math.atan2(direction.y, direction.x);
            const currentAngle = transform.rotation; // rotation is a number in 2.5D
            const angleDiff = this.normalizeAngle(targetAngle - currentAngle);
            const rotationSpeed = config.turnSpeed * deltaTime;

            let newAngle = currentAngle;
            if (Math.abs(angleDiff) > rotationSpeed) {
                newAngle += Math.sign(angleDiff) * rotationSpeed;
            } else {
                newAngle = targetAngle;
            }

            transform.setRotation(newAngle);
        }
    }
    
    /**
     * Normalize angle to [-PI, PI] range
     */
    private normalizeAngle(angle: number): number {
        while (angle > Math.PI) {angle -= 2 * Math.PI;}
        while (angle < -Math.PI) {angle += 2 * Math.PI;}
        return angle;
    }
    
    /**
     * Get movement configuration for entity
     */
    private getMovementConfig(entityId: string): MovementConfig {
        return this.movementConfigs.get(entityId) || this.defaultConfig;
    }
    
    /**
     * Set custom movement configuration for entity
     */
    public setMovementConfig(entityId: string, config: Partial<MovementConfig>): void {
        const existingConfig = this.getMovementConfig(entityId);
        const newConfig = { ...existingConfig, ...config };
        this.movementConfigs.set(entityId, newConfig);
    }
    
    /**
     * Remove movement configuration for entity
     */
    public removeMovementConfig(entityId: string): void {
        this.movementConfigs.delete(entityId);
    }
    
    /**
     * Set default movement configuration
     */
    public setDefaultConfig(config: Partial<MovementConfig>): void {
        this.defaultConfig = { ...this.defaultConfig, ...config };
    }
    
    /**
     * Cleanup movement system
     */
    public override cleanup(): void {
        this.movementConfigs.clear();
    }
}
