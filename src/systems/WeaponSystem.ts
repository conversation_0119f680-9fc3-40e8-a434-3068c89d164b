/**
 * WeaponSystem - Handles weapon mechanics, firing, reloading, and weapon management
 * Manages all weapon-related functionality for entities
 */

import { BaseSystem } from '@/core/BaseSystem';
import type { IGameEntity, Vector2, Vector3 } from '@/types';
import { InputComponent } from '@/components/InputComponent';
import { TransformComponent } from '@/components/TransformComponent';
import { PhysicsComponent } from '@/components/PhysicsComponent';
import { MeshComponent } from '@/components/MeshComponent';
import { EntityManager } from '@/core/EntityManager';
import { GameEntity } from '@/core/GameEntity';

interface WeaponStats {
    damage: number;
    fireRate: number; // rounds per second
    range: number;
    accuracy: number; // 0-1
    reloadTime: number; // seconds
    magazineSize: number;
    bulletSpeed: number;
    bulletLifetime: number; // seconds
}

interface WeaponState {
    currentAmmo: number;
    isReloading: boolean;
    reloadTimeRemaining: number;
    lastFireTime: number;
    overheated: boolean;
    heat: number;
}

interface WeaponConfig {
    weaponType: string;
    stats: WeaponStats;
    projectileType: 'bullet' | 'laser' | 'plasma' | 'rocket';
    fireMode: 'single' | 'auto' | 'burst';
    burstSize?: number;
}

export class WeaponSystem extends BaseSystem {
    public static readonly NAME = 'WeaponSystem';
    public static readonly PRIORITY = 4; // After collision system
    
    // Weapon configurations
    private weaponConfigs: Map<string, WeaponConfig> = new Map();
    
    // Entity weapon states
    private weaponStates: Map<string, WeaponState> = new Map();
    
    // Entity Manager reference for creating projectiles
    private entityManager: EntityManager | null = null;
    
    // Active projectiles for cleanup
    private activeProjectiles: Set<string> = new Set();
    
    constructor() {
        super(WeaponSystem.NAME, WeaponSystem.PRIORITY, ['transform']);
        this.setupDefaultWeapons();
    }
    
    /**
     * Initialize the weapon system
     */
    public override async initialize(): Promise<void> {
        // Get entity manager reference
        if (typeof window !== 'undefined' && (window as any).game?.entityManager) {
            this.entityManager = (window as any).game.entityManager;
        }
        
        console.log('WeaponSystem initialized');
    }
    
    /**
     * Update weapon system - handle firing, reloading, and weapon state
     */
    public update(entities: IGameEntity[], deltaTime: number): void {
        const weaponEntities = this.filterEntities(entities);
        
        for (const entity of weaponEntities) {
            // Update weapon state
            this.updateWeaponState(entity, deltaTime);
            
            // Process weapon input
            if (entity.hasComponent('input')) {
                this.processWeaponInput(entity, deltaTime);
            }
            
            // Update projectile lifetime
            this.updateProjectiles(entities, deltaTime);
        }
    }
    
    /**
     * Update weapon state (reloading, cooldown, heat, etc.)
     */
    private updateWeaponState(entity: IGameEntity, deltaTime: number): void {
        const weaponType = this.getEntityWeaponType(entity);
        if (!weaponType) {return;}
        
        const state = this.getWeaponState(entity.id);
        const config = this.weaponConfigs.get(weaponType);
        
        if (!state || !config) {return;}
        
        // Update reload
        if (state.isReloading) {
            state.reloadTimeRemaining -= deltaTime;
            
            if (state.reloadTimeRemaining <= 0) {
                state.isReloading = false;
                state.currentAmmo = config.stats.magazineSize;
                console.log(`${entity.id} finished reloading`);
            }
        }
        
        // Update heat (for overheating weapons)
        if (state.heat > 0) {
            state.heat = Math.max(0, state.heat - deltaTime * 2); // Cool down
            
            if (state.overheated && state.heat < 0.3) {
                state.overheated = false;
                console.log(`${entity.id} weapon cooled down`);
            }
        }
    }
    
    /**
     * Process weapon input from input component
     */
    private processWeaponInput(entity: IGameEntity, deltaTime: number): void {
        const input = entity.getComponent<InputComponent>('input');
        if (!input) {return;}
        
        // Handle shooting
        if (input.isActionPressed('shoot')) {
            this.attemptFire(entity);
        }
        
        // Handle reload
        if (input.isActionJustPressed('reload')) {
            this.attemptReload(entity);
        }
        
        // Handle weapon switching (if implemented)
        if (input.isActionJustPressed('nextWeapon')) {
            this.switchWeapon(entity, 1);
        }
        
        if (input.isActionJustPressed('prevWeapon')) {
            this.switchWeapon(entity, -1);
        }
    }
    
    /**
     * Attempt to fire weapon
     */
    private attemptFire(entity: IGameEntity): boolean {
        const weaponType = this.getEntityWeaponType(entity);
        if (!weaponType) {return false;}
        
        const state = this.getWeaponState(entity.id);
        const config = this.weaponConfigs.get(weaponType);
        
        if (!state || !config) {return false;}
        
        // Check if can fire
        if (!this.canFire(entity, state, config)) {
            return false;
        }
        
        // Fire weapon
        this.fireWeapon(entity, state, config);
        
        return true;
    }
    
    /**
     * Check if weapon can fire
     */
    private canFire(entity: IGameEntity, state: WeaponState, config: WeaponConfig): boolean {
        // Check ammo
        if (state.currentAmmo <= 0) {
            return false;
        }
        
        // Check reload state
        if (state.isReloading) {
            return false;
        }
        
        // Check overheat
        if (state.overheated) {
            return false;
        }
        
        // Check fire rate
        const currentTime = performance.now() / 1000;
        const timeSinceLastFire = currentTime - state.lastFireTime;
        const fireInterval = 1.0 / config.stats.fireRate;
        
        if (timeSinceLastFire < fireInterval) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Fire weapon and create projectile
     */
    private fireWeapon(entity: IGameEntity, state: WeaponState, config: WeaponConfig): void {
        const transform = entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        // Update weapon state
        state.currentAmmo--;
        state.lastFireTime = performance.now() / 1000;
        state.heat = Math.min(1.0, state.heat + 0.1); // Add heat
        
        if (state.heat >= 0.8) {
            state.overheated = true;
        }
        
        // Calculate firing direction
        const firingDirection = this.calculateFiringDirection(entity);
        
        // Apply accuracy spread
        const actualDirection = this.applyAccuracySpread(firingDirection, config.stats.accuracy);
        
        // Create projectile
        this.createProjectile(entity, transform.position, actualDirection, config);
        
        // Auto-reload if empty
        if (state.currentAmmo <= 0) {
            this.attemptReload(entity);
        }
        
        console.log(`${entity.id} fired weapon, ammo: ${state.currentAmmo}/${config.stats.magazineSize}`);
    }
    
    /**
     * Calculate firing direction based on entity orientation and input
     */
    private calculateFiringDirection(entity: IGameEntity): Vector2 {
        const transform = entity.getComponent<TransformComponent>('transform');
        const input = entity.getComponent<InputComponent>('input');
        
        if (!transform) {return { x: 1, y: 0 };}
        
        if (input) {
            // Use mouse position or look vector if available
            const lookVector = input.getLookVector();
            if (lookVector.x !== 0 || lookVector.y !== 0) {
                return lookVector;
            }
        }
        
        // Use entity rotation
        return {
            x: Math.cos(transform.rotation.z),
            y: Math.sin(transform.rotation.z)
        };
    }
    
    /**
     * Apply accuracy spread to firing direction
     */
    private applyAccuracySpread(direction: Vector2, accuracy: number): Vector2 {
        // Calculate spread angle (lower accuracy = more spread)
        const maxSpread = (1.0 - accuracy) * Math.PI * 0.2; // Up to 36 degrees spread
        const spread = (Math.random() - 0.5) * maxSpread;
        
        // Apply spread to direction
        const angle = Math.atan2(direction.y, direction.x) + spread;
        
        return {
            x: Math.cos(angle),
            y: Math.sin(angle)
        };
    }
    
    /**
     * Create projectile entity
     */
    private createProjectile(
        sourceEntity: IGameEntity,
        position: Vector3,
        direction: Vector2,
        config: WeaponConfig
    ): void {
        if (!this.entityManager) {return;}
        
        // Create projectile entity
        const projectile = new GameEntity(`bullet_${Date.now()}_${Math.random()}`, 'bullet');
        
        // Add transform component
        const transform = new TransformComponent(projectile);
        transform.position = { ...position };
        transform.rotation = { x: 0, y: 0, z: Math.atan2(direction.y, direction.x) };
        projectile.addComponent(transform);
        
        // Add physics component
        const physics = new PhysicsComponent(projectile);
        physics.velocity = {
            x: direction.x * config.stats.bulletSpeed,
            y: direction.y * config.stats.bulletSpeed
        };
        physics.mass = 0.1;
        physics.collisionLayer = 1; // Bullet layer
        physics.collisionMask = 2 | 4; // Collide with enemies and obstacles
        projectile.addComponent(physics);
        
        // Add mesh component for rendering
        const mesh = new MeshComponent(projectile);
        mesh.primitiveType = 'sphere';
        mesh.size = { x: 0.1, y: 0.1, z: 0.1 };
        mesh.color = { r: 1, g: 1, b: 0, a: 1 }; // Yellow bullet
        projectile.addComponent(mesh);
        
        // Store projectile metadata
        (projectile as any).damage = config.stats.damage;
        (projectile as any).source = sourceEntity.type;
        (projectile as any).sourceEntityId = sourceEntity.id;
        (projectile as any).lifetime = config.stats.bulletLifetime;
        (projectile as any).creationTime = performance.now() / 1000;
        
        // Add to entity manager
        this.entityManager.addEntity(projectile);
        this.activeProjectiles.add(projectile.id);
    }
    
    /**
     * Update projectile lifetime and cleanup
     */
    private updateProjectiles(entities: IGameEntity[], deltaTime: number): void {
        const currentTime = performance.now() / 1000;
        
        for (const entity of entities) {
            if (entity.type === 'bullet' && this.activeProjectiles.has(entity.id)) {
                const creationTime = (entity as any).creationTime || 0;
                const lifetime = (entity as any).lifetime || 5.0;
                
                if (currentTime - creationTime > lifetime) {
                    // Destroy projectile
                    entity.active = false;
                    entity.destroy();
                    this.activeProjectiles.delete(entity.id);
                }
            }
        }
    }
    
    /**
     * Attempt to reload weapon
     */
    private attemptReload(entity: IGameEntity): boolean {
        const weaponType = this.getEntityWeaponType(entity);
        if (!weaponType) {return false;}
        
        const state = this.getWeaponState(entity.id);
        const config = this.weaponConfigs.get(weaponType);
        
        if (!state || !config) {return false;}
        
        // Check if already reloading or full ammo
        if (state.isReloading || state.currentAmmo >= config.stats.magazineSize) {
            return false;
        }
        
        // Start reload
        state.isReloading = true;
        state.reloadTimeRemaining = config.stats.reloadTime;
        
        console.log(`${entity.id} started reloading (${config.stats.reloadTime}s)`);
        
        return true;
    }
    
    /**
     * Switch weapon for entity
     */
    private switchWeapon(entity: IGameEntity, direction: number): void {
        // This would implement weapon switching if entities can have multiple weapons
        // For now, just a placeholder
        console.log(`${entity.id} attempted to switch weapon`);
    }
    
    /**
     * Get entity weapon type
     */
    private getEntityWeaponType(entity: IGameEntity): string | null {
        // This would check entity's weapon component or metadata
        // For now, default based on entity type
        switch (entity.type) {
        case 'player':
            return 'assault_rifle';
        case 'enemy':
            return 'enemy_gun';
        default:
            return null;
        }
    }
    
    /**
     * Get or create weapon state for entity
     */
    private getWeaponState(entityId: string): WeaponState {
        let state = this.weaponStates.get(entityId);
        
        if (!state) {
            state = {
                currentAmmo: 30, // Default magazine size
                isReloading: false,
                reloadTimeRemaining: 0,
                lastFireTime: 0,
                overheated: false,
                heat: 0
            };
            this.weaponStates.set(entityId, state);
        }
        
        return state;
    }
    
    /**
     * Setup default weapon configurations
     */
    private setupDefaultWeapons(): void {
        // Assault Rifle (Player)
        this.weaponConfigs.set('assault_rifle', {
            weaponType: 'assault_rifle',
            stats: {
                damage: 25,
                fireRate: 8, // 8 rounds per second
                range: 50,
                accuracy: 0.85,
                reloadTime: 2.0,
                magazineSize: 30,
                bulletSpeed: 40,
                bulletLifetime: 3.0
            },
            projectileType: 'bullet',
            fireMode: 'auto'
        });
        
        // Enemy Gun
        this.weaponConfigs.set('enemy_gun', {
            weaponType: 'enemy_gun',
            stats: {
                damage: 15,
                fireRate: 3,
                range: 30,
                accuracy: 0.7,
                reloadTime: 1.5,
                magazineSize: 20,
                bulletSpeed: 30,
                bulletLifetime: 2.0
            },
            projectileType: 'bullet',
            fireMode: 'single'
        });
        
        // Shotgun
        this.weaponConfigs.set('shotgun', {
            weaponType: 'shotgun',
            stats: {
                damage: 40,
                fireRate: 1.5,
                range: 15,
                accuracy: 0.6,
                reloadTime: 3.0,
                magazineSize: 8,
                bulletSpeed: 25,
                bulletLifetime: 1.0
            },
            projectileType: 'bullet',
            fireMode: 'single'
        });
    }
    
    /**
     * Add custom weapon configuration
     */
    public addWeaponConfig(weaponType: string, config: WeaponConfig): void {
        this.weaponConfigs.set(weaponType, config);
    }
    
    /**
     * Get weapon state for entity (read-only)
     */
    public getEntityWeaponState(entityId: string): Readonly<WeaponState> | null {
        return this.weaponStates.get(entityId) || null;
    }
    
    /**
     * Get weapon config (read-only)
     */
    public getWeaponConfig(weaponType: string): Readonly<WeaponConfig> | null {
        return this.weaponConfigs.get(weaponType) || null;
    }
    
    /**
     * Force reload for entity
     */
    public forceReload(entityId: string): boolean {
        const entity = { id: entityId } as IGameEntity; // Simplified for method call
        return this.attemptReload(entity);
    }
    
    /**
     * Set entity manager reference
     */
    public setEntityManager(entityManager: EntityManager): void {
        this.entityManager = entityManager;
    }
    
    /**
     * Cleanup weapon system
     */
    public override cleanup(): void {
        this.weaponStates.clear();
        this.activeProjectiles.clear();
        this.entityManager = null;
    }
}
