/**
 * Systems Export Index
 * Central export point for all game systems in the ECS architecture
 */

// Core systems
export { PhysicsSystem } from './PhysicsSystem';
export { InputSystem } from './InputSystem';
export { MovementSystem } from './MovementSystem';
export { RenderSystem } from './RenderSystem';
export { CollisionSystem } from './CollisionSystem';
export { WeaponSystem } from './WeaponSystem';

// Re-export system interfaces and types
export type {
    ISystem,
    SystemStatus
} from '@/types/CoreInterfaces.types';
