export class UIManager {
    constructor() {
        this.activeMenu = null;
        this.eventListeners = new Map();

        // UI elements
        this.elements = {
            mainMenu: null,
            pauseMenu: null,
            gameOver: null,
            hud: null,
        };

        // Button references
        this.buttons = {
            startGame: null,
            options: null,
            credits: null,
            restartGame: null,
            backToMenu: null,
            resumeGame: null,
            pauseOptions: null,
            quitToMenu: null,
        };
    }

    async init() {
        // Get UI element references
        this.cacheElements();

        // Set up event listeners
        this.setupEventListeners();

        console.log('UIManager initialized');
    }

    cacheElements() {
        // Menu elements
        this.elements.mainMenu = document.getElementById('main-menu');
        this.elements.pauseMenu = document.getElementById('pause-menu');
        this.elements.gameOver = document.getElementById('game-over');
        this.elements.hud = document.getElementById('hud');

        // Button elements
        this.buttons.startGame = document.getElementById('start-game');
        this.buttons.options = document.getElementById('options');
        this.buttons.credits = document.getElementById('credits');
        this.buttons.restartGame = document.getElementById('restart-game');
        this.buttons.backToMenu = document.getElementById('back-to-menu');
        this.buttons.resumeGame = document.getElementById('resume-game');
        this.buttons.pauseOptions = document.getElementById('pause-options');
        this.buttons.quitToMenu = document.getElementById('quit-to-menu');
    }

    setupEventListeners() {
        // Main menu buttons
        if (this.buttons.startGame) {
            this.addButtonListener('startGame', () => this.onStartGame());
        }

        if (this.buttons.options) {
            this.addButtonListener('options', () => this.onOptions());
        }

        if (this.buttons.credits) {
            this.addButtonListener('credits', () => this.onCredits());
        }

        // Game over buttons
        if (this.buttons.restartGame) {
            this.addButtonListener('restartGame', () => this.onRestartGame());
        }

        if (this.buttons.backToMenu) {
            this.addButtonListener('backToMenu', () => this.onBackToMenu());
        }

        // Pause menu buttons
        if (this.buttons.resumeGame) {
            this.addButtonListener('resumeGame', () => this.onResumeGame());
        }

        if (this.buttons.pauseOptions) {
            this.addButtonListener('pauseOptions', () => this.onPauseOptions());
        }

        if (this.buttons.quitToMenu) {
            this.addButtonListener('quitToMenu', () => this.onQuitToMenu());
        }

        // Global keyboard shortcuts
        document.addEventListener('keydown', event => {
            this.handleGlobalKeydown(event);
        });
    }

    addButtonListener(buttonKey, handler) {
        const button = this.buttons[buttonKey];
        if (!button) {
            return;
        }

        const wrappedHandler = event => {
            this.playButtonSound();
            handler(event);
        };

        button.addEventListener('click', wrappedHandler);
        this.eventListeners.set(buttonKey, { element: button, handler: wrappedHandler });

        // Add hover effects
        button.addEventListener('mouseenter', () => {
            this.playHoverSound();
        });
    }

    handleGlobalKeydown(event) {
        // Handle global shortcuts
        switch (event.code) {
            case 'Escape':
                this.handleEscapeKey();
                break;
            case 'Enter':
                this.handleEnterKey();
                break;
        }
    }

    handleEscapeKey() {
        const game = window.game;
        if (!game) {
            return;
        }

        const currentState = game.gameStateManager.getCurrentState();

        switch (currentState) {
            case 'playing':
                game.pauseGame();
                break;
            case 'paused':
                game.resumeGame();
                break;
        }
    }

    handleEnterKey() {
        // Handle enter key for menu navigation
        const currentMenu = this.getCurrentMenu();
        if (currentMenu) {
            const primaryButton = currentMenu.querySelector('.menu-btn.primary');
            if (primaryButton) {
                primaryButton.click();
            }
        }
    }

    getCurrentMenu() {
        if (!this.elements.mainMenu?.classList.contains('hidden')) {
            return this.elements.mainMenu;
        }
        if (!this.elements.pauseMenu?.classList.contains('hidden')) {
            return this.elements.pauseMenu;
        }
        if (!this.elements.gameOver?.classList.contains('hidden')) {
            return this.elements.gameOver;
        }
        return null;
    }

    // Button event handlers
    onStartGame() {
        const game = window.game;
        if (game) {
            game.gameStateManager.setState('playing');
        }
    }

    onOptions() {
        // TODO: Implement options menu
        console.log('Options clicked');
    }

    onCredits() {
        // TODO: Implement credits screen
        console.log('Credits clicked');
    }

    onRestartGame() {
        const game = window.game;
        if (game) {
            game.restartGame();
        }
    }

    onBackToMenu() {
        const game = window.game;
        if (game) {
            game.gameStateManager.setState('menu');
        }
    }

    onResumeGame() {
        const game = window.game;
        if (game) {
            game.resumeGame();
        }
    }

    onPauseOptions() {
        // TODO: Implement pause options
        console.log('Pause options clicked');
    }

    onQuitToMenu() {
        const game = window.game;
        if (game) {
            game.gameStateManager.setState('menu');
        }
    }

    // Audio feedback
    playButtonSound() {
        const game = window.game;
        if (game?.audioManager) {
            game.audioManager.playMenuConfirm();
        }
    }

    playHoverSound() {
        const game = window.game;
        if (game?.audioManager) {
            game.audioManager.playMenuSelect();
        }
    }

    // HUD update methods
    updateHealth(health, maxHealth) {
        const healthFill = document.querySelector('.health-fill');
        const healthText = document.querySelector('.health-text');

        if (healthFill && healthText) {
            const healthPercent = (health / maxHealth) * 100;
            healthFill.style.width = `${healthPercent}%`;
            healthText.textContent = Math.ceil(health);
        }
    }

    updateScore(score) {
        const scoreValue = document.querySelector('.score-value');
        if (scoreValue) {
            scoreValue.textContent = score.toLocaleString();
        }
    }

    updateLives(lives) {
        const livesValue = document.querySelector('.lives-value');
        if (livesValue) {
            livesValue.textContent = lives;
        }
    }

    updateWeapon(weaponName, ammo, maxAmmo) {
        const weaponNameElement = document.querySelector('.weapon-name');
        const ammoFill = document.querySelector('.ammo-fill');

        if (weaponNameElement) {
            weaponNameElement.textContent = weaponName;
        }

        if (ammoFill && maxAmmo > 0) {
            const ammoPercent = (ammo / maxAmmo) * 100;
            ammoFill.style.width = `${ammoPercent}%`;
        }
    }

    // Animation helpers
    showElement(element, animation = 'fadeIn') {
        if (!element) {
            return;
        }

        element.classList.remove('hidden');

        switch (animation) {
            case 'fadeIn':
                element.style.opacity = '0';
                element.style.transition = 'opacity 0.3s ease';
                setTimeout(() => {
                    element.style.opacity = '1';
                }, 10);
                break;
            case 'slideIn':
                element.style.transform = 'translateY(-20px)';
                element.style.transition = 'transform 0.3s ease';
                setTimeout(() => {
                    element.style.transform = 'translateY(0)';
                }, 10);
                break;
        }
    }

    hideElement(element, animation = 'fadeOut') {
        if (!element) {
            return;
        }

        switch (animation) {
            case 'fadeOut':
                element.style.transition = 'opacity 0.3s ease';
                element.style.opacity = '0';
                setTimeout(() => {
                    element.classList.add('hidden');
                    element.style.opacity = '1';
                }, 300);
                break;
            case 'slideOut':
                element.style.transition = 'transform 0.3s ease';
                element.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    element.classList.add('hidden');
                    element.style.transform = 'translateY(0)';
                }, 300);
                break;
            default:
                element.classList.add('hidden');
                break;
        }
    }

    update(deltaTime) {
        // Update any animated UI elements
        this.updateAnimations(deltaTime);
    }

    updateAnimations(_deltaTime) {
        // Placeholder for UI animations
        // Could include things like pulsing health bars, animated scores, etc.
    }

    // Cleanup
    destroy() {
        // Remove all event listeners
        this.eventListeners.forEach(({ element, handler }) => {
            element.removeEventListener('click', handler);
        });
        this.eventListeners.clear();

        console.log('UIManager destroyed');
    }
}
