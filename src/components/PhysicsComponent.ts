/**
 * PhysicsComponent - Handles physics simulation for entities
 * Manages velocity, collision detection, and physics properties
 */

import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity, Vector2 } from '@/types';
import { TransformComponent } from './TransformComponent';

export interface CollisionShape {
    type: 'circle' | 'rectangle' | 'polygon';
    radius?: number; // For circle
    width?: number; // For rectangle
    height?: number; // For rectangle
    vertices?: Vector2[]; // For polygon
}

export class PhysicsComponent extends BaseComponent {
    public static readonly TYPE = 'physics';
    
    // Motion properties
    public velocity: Vector2;
    public acceleration: Vector2;
    public angularVelocity: number = 0;
    public angularAcceleration: number = 0;
    
    // Physical properties
    public mass: number;
    public drag: number = 0.98; // Air resistance (0 = no drag, 1 = infinite drag)
    public angularDrag: number = 0.98;
    public restitution: number = 0.8; // Bounciness (0 = no bounce, 1 = perfect bounce)
    public friction: number = 0.1;
    
    // Collision properties
    public collisionShape: CollisionShape;
    public collisionLayer: number = 0; // What layer this object is on
    public collisionMask: number = 0xFFFFFFFF; // What layers this object collides with
    public isTrigger: boolean = false; // If true, detects collisions but doesn't respond
    public isStatic: boolean = false; // If true, object doesn't move
    public override enabled: boolean = true;

    // Additional properties expected by systems
    public applyDrag: boolean = true;
    
    // Constraints
    public maxVelocity: number = Infinity;
    public maxAngularVelocity: number = Infinity;
    
    // Gravity
    public useGravity: boolean = false;
    public gravityScale: number = 1;
    
    // Collision events
    public onCollisionEnter?: (other: PhysicsComponent) => void;
    public onCollisionExit?: (other: PhysicsComponent) => void;
    public onTriggerEnter?: (other: PhysicsComponent) => void;
    public onTriggerExit?: (other: PhysicsComponent) => void;
    
    // Internal state
    private previousPosition: Vector2;
    private currentCollisions: Set<string> = new Set();
    private collisionEvents: Array<{ entityA: IGameEntity; entityB: IGameEntity; contactPoint?: Vector2; normal?: Vector2 }> = [];
    
    constructor(
        entity: IGameEntity, 
        mass: number = 1, 
        shape: CollisionShape = { type: 'circle', radius: 0.5 }
    ) {
        super(entity, PhysicsComponent.TYPE);
        
        this.velocity = { x: 0, y: 0 };
        this.acceleration = { x: 0, y: 0 };
        this.mass = mass;
        this.collisionShape = shape;
        this.previousPosition = { ...entity.position };
    }
    
    /**
     * Update physics simulation
     */
    public update(deltaTime: number): void {
        if (!this.enabled || this.isStatic) {return;}
        
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        // Store previous position for collision detection
        this.previousPosition = transform.getPosition();
        
        // Apply acceleration to velocity
        this.velocity.x += this.acceleration.x * deltaTime;
        this.velocity.y += this.acceleration.y * deltaTime;
        this.angularVelocity += this.angularAcceleration * deltaTime;
        
        // Apply drag
        this.velocity.x *= this.drag;
        this.velocity.y *= this.drag;
        this.angularVelocity *= this.angularDrag;
        
        // Limit velocities
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);
        if (speed > this.maxVelocity) {
            const factor = this.maxVelocity / speed;
            this.velocity.x *= factor;
            this.velocity.y *= factor;
        }
        
        if (Math.abs(this.angularVelocity) > this.maxAngularVelocity) {
            this.angularVelocity = Math.sign(this.angularVelocity) * this.maxAngularVelocity;
        }
        
        // Apply velocity to position
        const newX = transform.getPosition().x + this.velocity.x * deltaTime;
        const newY = transform.getPosition().y + this.velocity.y * deltaTime;
        transform.setPosition(newX, newY);
        
        // Apply angular velocity to rotation
        const newRotation = transform.getRotation() + this.angularVelocity * deltaTime;
        transform.setRotation(newRotation);
        
        // Reset acceleration (forces are applied each frame)
        this.acceleration.x = 0;
        this.acceleration.y = 0;
        this.angularAcceleration = 0;
    }
    
    /**
     * Apply force to the physics body
     */
    public addForce(force: Vector2): void {
        if (this.isStatic || this.mass <= 0) {return;}
        
        this.acceleration.x += force.x / this.mass;
        this.acceleration.y += force.y / this.mass;
    }
    
    /**
     * Apply impulse (instantaneous change in velocity)
     */
    public addImpulse(impulse: Vector2): void {
        if (this.isStatic || this.mass <= 0) {return;}
        
        this.velocity.x += impulse.x / this.mass;
        this.velocity.y += impulse.y / this.mass;
    }
    
    /**
     * Apply torque (rotational force)
     */
    public addTorque(torque: number): void {
        if (this.isStatic || this.mass <= 0) {return;}
        
        // Simplified torque calculation (assuming uniform density)
        const momentOfInertia = this.mass * 0.5; // Approximation
        this.angularAcceleration += torque / momentOfInertia;
    }
    
    /**
     * Set velocity directly
     */
    public setVelocity(velocity: Vector2): void {
        this.velocity.x = velocity.x;
        this.velocity.y = velocity.y;
    }
    
    /**
     * Get current speed (magnitude of velocity)
     */
    public getSpeed(): number {
        return Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);
    }
    
    /**
     * Get velocity direction (normalized)
     */
    public getVelocityDirection(): Vector2 {
        const speed = this.getSpeed();
        if (speed === 0) {return { x: 0, y: 0 };}

        return {
            x: this.velocity.x / speed,
            y: this.velocity.y / speed
        };
    }

    /**
     * Get collision events for this frame (expected by CollisionSystem)
     */
    public getCollisionEvents(): Array<{ entityA: IGameEntity; entityB: IGameEntity; contactPoint?: Vector2; normal?: Vector2 }> {
        return [...this.collisionEvents];
    }

    /**
     * Clear collision events (called by physics system after processing)
     */
    public clearCollisionEvents(): void {
        this.collisionEvents.length = 0;
    }
    
    /**
     * Check if this physics body overlaps with another
     */
    public overlaps(other: PhysicsComponent): boolean {
        const transform = this.entity.getComponent<TransformComponent>('transform');
        const otherTransform = other.entity.getComponent<TransformComponent>('transform');
        
        if (!transform || !otherTransform) {return false;}
        
        const pos1 = transform.getWorldPosition();
        const pos2 = otherTransform.getWorldPosition();
        
        return this.checkShapeOverlap(pos1, this.collisionShape, pos2, other.collisionShape);
    }
    
    /**
     * Get collision bounds (axis-aligned bounding box)
     */
    public getBounds(): { min: Vector2; max: Vector2 } {
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {
            return { min: { x: 0, y: 0 }, max: { x: 0, y: 0 } };
        }
        
        const pos = transform.getWorldPosition();
        const scale = transform.getWorldScale();
        
        switch (this.collisionShape.type) {
        case 'circle':
            const radius = (this.collisionShape.radius || 0.5) * Math.max(scale.x, scale.y);
            return {
                min: { x: pos.x - radius, y: pos.y - radius },
                max: { x: pos.x + radius, y: pos.y + radius }
            };
                
        case 'rectangle':
            const halfWidth = (this.collisionShape.width || 1) * scale.x * 0.5;
            const halfHeight = (this.collisionShape.height || 1) * scale.y * 0.5;
            return {
                min: { x: pos.x - halfWidth, y: pos.y - halfHeight },
                max: { x: pos.x + halfWidth, y: pos.y + halfHeight }
            };
                
        default:
            return { min: { x: pos.x, y: pos.y }, max: { x: pos.x, y: pos.y } };
        }
    }
    
    /**
     * Handle collision with another physics component
     */
    public handleCollision(other: PhysicsComponent, contactPoint?: Vector2, normal?: Vector2): void {
        const otherEntityId = other.entity.id;
        const wasColliding = this.currentCollisions.has(otherEntityId);
        
        if (!wasColliding) {
            // New collision
            this.currentCollisions.add(otherEntityId);
            
            if (this.isTrigger || other.isTrigger) {
                this.onTriggerEnter?.(other);
                other.onTriggerEnter?.(this);
            } else {
                this.onCollisionEnter?.(other);
                other.onCollisionEnter?.(this);
                
                // Apply collision response
                this.resolveCollision(other, contactPoint, normal);
            }
        }
    }
    
    /**
     * Handle collision exit
     */
    public handleCollisionExit(other: PhysicsComponent): void {
        const otherEntityId = other.entity.id;
        
        if (this.currentCollisions.has(otherEntityId)) {
            this.currentCollisions.delete(otherEntityId);
            
            if (this.isTrigger || other.isTrigger) {
                this.onTriggerExit?.(other);
                other.onTriggerExit?.(this);
            } else {
                this.onCollisionExit?.(other);
                other.onCollisionExit?.(this);
            }
        }
    }
    
    /**
     * Resolve collision physics
     */
    private resolveCollision(other: PhysicsComponent, contactPoint?: Vector2, normal?: Vector2): void {
        if (this.isStatic && other.isStatic) {return;}
        
        // Calculate relative velocity
        const relativeVelocity = {
            x: this.velocity.x - other.velocity.x,
            y: this.velocity.y - other.velocity.y
        };
        
        // Use provided normal or calculate from positions
        let collisionNormal = normal;
        if (!collisionNormal) {
            const transform = this.entity.getComponent<TransformComponent>('transform');
            const otherTransform = other.entity.getComponent<TransformComponent>('transform');
            
            if (transform && otherTransform) {
                const pos1 = transform.getWorldPosition();
                const pos2 = otherTransform.getWorldPosition();
                const dx = pos1.x - pos2.x;
                const dy = pos1.y - pos2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    collisionNormal = { x: dx / distance, y: dy / distance };
                } else {
                    collisionNormal = { x: 1, y: 0 };
                }
            } else {
                collisionNormal = { x: 1, y: 0 };
            }
        }
        
        // Calculate relative velocity along normal
        const velocityAlongNormal = relativeVelocity.x * collisionNormal.x + relativeVelocity.y * collisionNormal.y;
        
        // Don't resolve if velocities are separating
        if (velocityAlongNormal > 0) {return;}
        
        // Calculate restitution
        const e = Math.min(this.restitution, other.restitution);
        
        // Calculate impulse scalar
        let j = -(1 + e) * velocityAlongNormal;
        j /= (1 / this.mass) + (1 / other.mass);
        
        // Apply impulse
        const impulse = { x: j * collisionNormal.x, y: j * collisionNormal.y };
        
        if (!this.isStatic) {
            this.velocity.x += impulse.x / this.mass;
            this.velocity.y += impulse.y / this.mass;
        }
        
        if (!other.isStatic) {
            other.velocity.x -= impulse.x / other.mass;
            other.velocity.y -= impulse.y / other.mass;
        }
    }
    
    /**
     * Check if two shapes overlap
     */
    private checkShapeOverlap(pos1: Vector2, shape1: CollisionShape, pos2: Vector2, shape2: CollisionShape): boolean {
        // Simple circle-circle collision for now
        if (shape1.type === 'circle' && shape2.type === 'circle') {
            const dx = pos1.x - pos2.x;
            const dy = pos1.y - pos2.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const radius1 = shape1.radius || 0.5;
            const radius2 = shape2.radius || 0.5;
            return distance < (radius1 + radius2);
        }
        
        // TODO: Implement rectangle-rectangle, circle-rectangle, polygon collision detection
        return false;
    }
    
    /**
     * Serialize physics component data
     */
    public override serialize(): Record<string, unknown> {
        return {
            ...super.serialize(),
            velocity: this.velocity,
            acceleration: this.acceleration,
            angularVelocity: this.angularVelocity,
            mass: this.mass,
            drag: this.drag,
            restitution: this.restitution,
            friction: this.friction,
            collisionShape: this.collisionShape,
            collisionLayer: this.collisionLayer,
            collisionMask: this.collisionMask,
            isTrigger: this.isTrigger,
            isStatic: this.isStatic,
            useGravity: this.useGravity,
            gravityScale: this.gravityScale
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Clear collision tracking
        this.currentCollisions.clear();
        this.collisionEvents.length = 0;

        // Remove callbacks (using delete to avoid exactOptionalPropertyTypes issues)
        delete this.onCollisionEnter;
        delete this.onCollisionExit;
        delete this.onTriggerEnter;
        delete this.onTriggerExit;
    }
}
