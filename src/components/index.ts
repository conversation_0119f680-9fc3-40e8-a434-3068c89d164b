/**
 * Components index - exports all game components
 */

// Base component
export { BaseComponent } from '@/core/BaseComponent';

// Core components
export { TransformComponent } from './TransformComponent';
export { MeshComponent } from './MeshComponent';
export { PhysicsComponent } from './PhysicsComponent';
export { HealthComponent, type DamageInfo } from './HealthComponent';
export { InputComponent, type InputAction, type InputAxis } from './InputComponent';
export { AIComponent, AIBehaviorType, type AIBehavior, type AITarget } from './AIComponent';

// Component types for easy reference
export const ComponentTypes = {
    TRANSFORM: 'transform',
    MESH: 'mesh',
    PHYSICS: 'physics',
    HEALTH: 'health',
    INPUT: 'input',
    AI: 'ai'
} as const;
