/**
 * InputComponent - <PERSON>les input processing for entities
 * Manages keyboard, mouse, and gamepad input for player and AI entities
 */

import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity, Vector2 } from '@/types';

export interface InputAction {
    name: string;
    keys: string[]; // Keyboard keys
    mouseButtons?: number[]; // Mouse buttons
    gamepadButtons?: number[]; // Gamepad buttons
    isPressed: boolean;
    wasPressed: boolean; // Was pressed this frame
    wasReleased: boolean; // Was released this frame
    holdTime: number; // How long the action has been held
}

export interface InputAxis {
    name: string;
    positiveKeys: string[];
    negativeKeys: string[];
    value: number; // -1 to 1
    deadZone: number;
    sensitivity: number;
}

export class InputComponent extends BaseComponent {
    public static readonly TYPE = 'input';
    
    // Input actions (discrete buttons)
    public actions: Map<string, InputAction> = new Map();
    
    // Input axes (continuous values)
    public axes: Map<string, InputAxis> = new Map();
    
    // Mouse input
    public mousePosition: Vector2 = { x: 0, y: 0 };
    public mouseDelta: Vector2 = { x: 0, y: 0 };
    public mouseWorldPosition: Vector2 = { x: 0, y: 0 };
    
    // Input state
    public inputEnabled: boolean = true;
    public isPlayer: boolean = false; // If true, receives actual input; if false, can be driven by AI
    
    // Input buffering (for responsive controls)
    public inputBufferTime: number = 0.1; // Seconds to buffer inputs
    private bufferedActions: Map<string, number> = new Map(); // Action name -> time pressed
    
    // Events
    public onActionPressed?: ((actionName: string) => void) | undefined;
    public onActionReleased?: ((actionName: string) => void) | undefined;
    public onAxisChanged?: ((axisName: string, value: number) => void) | undefined;
    
    constructor(entity: IGameEntity, isPlayer: boolean = false) {
        super(entity, InputComponent.TYPE);
        this.isPlayer = isPlayer;
        
        if (isPlayer) {
            this.setupDefaultPlayerControls();
        }
    }
    
    /**
     * Update input state
     */
    public update(deltaTime: number): void {
        if (!this.enabled || !this.inputEnabled) {
            return;
        }
        
        // Update action hold times and clear frame-specific flags
        for (const action of this.actions.values()) {
            action.wasPressed = false;
            action.wasReleased = false;
            
            if (action.isPressed) {
                action.holdTime += deltaTime;
            }
        }
        
        // Process buffered inputs
        this.processBufferedInputs(deltaTime);
        
        // Update axes
        this.updateAxes();
    }
    
    /**
     * Define an input action
     */
    public defineAction(
        name: string,
        keys: string[],
        mouseButtons?: number[],
        gamepadButtons?: number[]
    ): void {
        const action: InputAction = {
            name,
            keys,
            isPressed: false,
            wasPressed: false,
            wasReleased: false,
            holdTime: 0
        };

        // Only set optional properties if they are provided
        if (mouseButtons !== undefined) {
            action.mouseButtons = mouseButtons;
        }
        if (gamepadButtons !== undefined) {
            action.gamepadButtons = gamepadButtons;
        }

        this.actions.set(name, action);
    }
    
    /**
     * Define an input axis
     */
    public defineAxis(
        name: string, 
        positiveKeys: string[], 
        negativeKeys: string[], 
        deadZone: number = 0.1,
        sensitivity: number = 1.0
    ): void {
        this.axes.set(name, {
            name,
            positiveKeys,
            negativeKeys,
            value: 0,
            deadZone,
            sensitivity
        });
    }
    
    /**
     * Check if an action is currently pressed
     */
    public isActionPressed(actionName: string): boolean {
        const action = this.actions.get(actionName);
        return action ? action.isPressed : false;
    }
    
    /**
     * Check if an action was just pressed this frame
     */
    public wasActionPressed(actionName: string): boolean {
        const action = this.actions.get(actionName);
        return action ? action.wasPressed : false;
    }

    /**
     * Check if an action was just pressed this frame (alias for wasActionPressed)
     */
    public isActionJustPressed(actionName: string): boolean {
        return this.wasActionPressed(actionName);
    }
    
    /**
     * Check if an action was just released this frame
     */
    public wasActionReleased(actionName: string): boolean {
        const action = this.actions.get(actionName);
        return action ? action.wasReleased : false;
    }
    
    /**
     * Get how long an action has been held
     */
    public getActionHoldTime(actionName: string): number {
        const action = this.actions.get(actionName);
        return action ? action.holdTime : 0;
    }
    
    /**
     * Get axis value (-1 to 1)
     */
    public getAxisValue(axisName: string): number {
        const axis = this.axes.get(axisName);
        return axis ? axis.value : 0;
    }
    
    /**
     * Simulate action press (for AI or scripted input)
     */
    public simulateActionPress(actionName: string): void {
        const action = this.actions.get(actionName);
        if (action && !action.isPressed) {
            action.isPressed = true;
            action.wasPressed = true;
            action.holdTime = 0;
            this.onActionPressed?.(actionName);
        }
    }
    
    /**
     * Simulate action release (for AI or scripted input)
     */
    public simulateActionRelease(actionName: string): void {
        const action = this.actions.get(actionName);
        if (action && action.isPressed) {
            action.isPressed = false;
            action.wasReleased = true;
            this.onActionReleased?.(actionName);
        }
    }
    
    /**
     * Set axis value directly (for AI or scripted input)
     */
    public setAxisValue(axisName: string, value: number): void {
        const axis = this.axes.get(axisName);
        if (axis) {
            const oldValue = axis.value;
            axis.value = Math.max(-1, Math.min(1, value));
            
            if (axis.value !== oldValue) {
                this.onAxisChanged?.(axisName, axis.value);
            }
        }
    }
    
    /**
     * Handle key down event (called by input system)
     */
    public handleKeyDown(key: string): void {
        if (!this.inputEnabled || !this.isPlayer) {
            return;
        }
        
        // Check actions
        for (const action of this.actions.values()) {
            if (action.keys.includes(key) && !action.isPressed) {
                action.isPressed = true;
                action.wasPressed = true;
                action.holdTime = 0;
                this.onActionPressed?.(action.name);
                
                // Add to buffer
                this.bufferedActions.set(action.name, performance.now() / 1000);
            }
        }
    }
    
    /**
     * Handle key up event (called by input system)
     */
    public handleKeyUp(key: string): void {
        if (!this.inputEnabled || !this.isPlayer) {
            return;
        }
        
        // Check actions
        for (const action of this.actions.values()) {
            if (action.keys.includes(key) && action.isPressed) {
                action.isPressed = false;
                action.wasReleased = true;
                this.onActionReleased?.(action.name);
            }
        }
    }
    
    /**
     * Handle mouse button down event
     */
    public handleMouseDown(button: number): void {
        if (!this.inputEnabled || !this.isPlayer) {
            return;
        }
        
        for (const action of this.actions.values()) {
            if (action.mouseButtons?.includes(button) && !action.isPressed) {
                action.isPressed = true;
                action.wasPressed = true;
                action.holdTime = 0;
                this.onActionPressed?.(action.name);
            }
        }
    }
    
    /**
     * Handle mouse button up event
     */
    public handleMouseUp(button: number): void {
        if (!this.inputEnabled || !this.isPlayer) {
            return;
        }
        
        for (const action of this.actions.values()) {
            if (action.mouseButtons?.includes(button) && action.isPressed) {
                action.isPressed = false;
                action.wasReleased = true;
                this.onActionReleased?.(action.name);
            }
        }
    }
    
    /**
     * Handle mouse move event
     */
    public handleMouseMove(position: Vector2, delta: Vector2): void {
        if (!this.inputEnabled || !this.isPlayer) {
            return;
        }
        
        this.mousePosition = { ...position };
        this.mouseDelta = { ...delta };
        
        // TODO: Convert screen position to world position
        // This would require camera information
        this.mouseWorldPosition = { ...position };
    }
    
    /**
     * Get movement vector from movement axes
     */
    public getMovementVector(): Vector2 {
        return {
            x: this.getAxisValue('horizontal'),
            y: this.getAxisValue('vertical')
        };
    }
    
    /**
     * Get look vector (for aiming)
     */
    public getLookVector(): Vector2 {
        return {
            x: this.getAxisValue('lookHorizontal'),
            y: this.getAxisValue('lookVertical')
        };
    }
    
    /**
     * Process buffered inputs
     */
    private processBufferedInputs(deltaTime: number): void {
        const currentTime = performance.now() / 1000;
        
        for (const [actionName, pressTime] of this.bufferedActions.entries()) {
            if (currentTime - pressTime > this.inputBufferTime) {
                this.bufferedActions.delete(actionName);
            }
        }
    }
    
    /**
     * Update axis values based on key states
     */
    private updateAxes(): void {
        if (!this.isPlayer) {
            return;
        }
        
        for (const axis of this.axes.values()) {
            let value = 0;
            
            // Check positive keys
            for (const key of axis.positiveKeys) {
                if (this.isKeyPressed(key)) {
                    value += 1;
                    break;
                }
            }
            
            // Check negative keys
            for (const key of axis.negativeKeys) {
                if (this.isKeyPressed(key)) {
                    value -= 1;
                    break;
                }
            }
            
            // Apply sensitivity and dead zone
            value *= axis.sensitivity;
            if (Math.abs(value) < axis.deadZone) {
                value = 0;
            }
            
            // Update axis value
            if (axis.value !== value) {
                axis.value = value;
                this.onAxisChanged?.(axis.name, value);
            }
        }
    }
    
    /**
     * Check if a key is currently pressed (would be handled by input manager)
     */
    private isKeyPressed(_key: string): boolean {
        // This would need to be connected to the global input manager
        // For now, return false - this will be connected in the input system
        return false;
    }
    
    /**
     * Setup default player controls
     */
    private setupDefaultPlayerControls(): void {
        // Movement actions
        this.defineAction('moveUp', ['KeyW', 'ArrowUp']);
        this.defineAction('moveDown', ['KeyS', 'ArrowDown']);
        this.defineAction('moveLeft', ['KeyA', 'ArrowLeft']);
        this.defineAction('moveRight', ['KeyD', 'ArrowRight']);
        
        // Combat actions
        this.defineAction('shoot', ['Space'], [0]); // Left mouse button
        this.defineAction('altFire', ['ShiftLeft'], [2]); // Right mouse button
        this.defineAction('reload', ['KeyR']);
        
        // Special actions
        this.defineAction('dash', ['ShiftLeft']);
        this.defineAction('interact', ['KeyE']);
        this.defineAction('pause', ['Escape']);
        
        // Movement axes
        this.defineAxis('horizontal', ['KeyD', 'ArrowRight'], ['KeyA', 'ArrowLeft']);
        this.defineAxis('vertical', ['KeyW', 'ArrowUp'], ['KeyS', 'ArrowDown']);
        
        // Look axes (for twin-stick controls or mouse look)
        this.defineAxis('lookHorizontal', [], []);
        this.defineAxis('lookVertical', [], []);
    }
    
    /**
     * Serialize input component data
     */
    public override serialize(): Record<string, unknown> {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const actionsData: Record<string, any> = {};
        for (const [name, action] of this.actions) {
            actionsData[name] = {
                keys: action.keys,
                mouseButtons: action.mouseButtons,
                gamepadButtons: action.gamepadButtons
            };
        }
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const axesData: Record<string, any> = {};
        for (const [name, axis] of this.axes) {
            axesData[name] = {
                positiveKeys: axis.positiveKeys,
                negativeKeys: axis.negativeKeys,
                deadZone: axis.deadZone,
                sensitivity: axis.sensitivity
            };
        }
        
        return {
            ...super.serialize(),
            actions: actionsData,
            axes: axesData,
            inputEnabled: this.inputEnabled,
            isPlayer: this.isPlayer,
            inputBufferTime: this.inputBufferTime
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Clear event handlers
        this.onActionPressed = undefined;
        this.onActionReleased = undefined;
        this.onAxisChanged = undefined;
        
        // Clear collections
        this.actions.clear();
        this.axes.clear();
        this.bufferedActions.clear();
    }
}
