/**
 * AIComponent - Handles AI behavior and decision making
 * Manages AI states, behaviors, and reactions for non-player entities
 */

import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity, Vector2 } from '@/types';
import { InputComponent } from './InputComponent';
import { TransformComponent } from './TransformComponent';

export enum AIBehaviorType {
    IDLE = 'idle',
    PATROL = 'patrol',
    CHASE = 'chase',
    ATTACK = 'attack',
    FLEE = 'flee',
    INVESTIGATE = 'investigate',
    GUARD = 'guard',
    FOLLOW = 'follow'
}

export interface AIBehavior {
    type: AIBehaviorType;
    priority: number;
    canActivate: (ai: AIComponent) => boolean;
    execute: (ai: AIComponent, deltaTime: number) => void;
    onEnter?: (ai: AIComponent) => void;
    onExit?: (ai: AIComponent) => void;
}

export interface AITarget {
    entity: IGameEntity;
    priority: number;
    lastSeenPosition: Vector2;
    lastSeenTime: number;
    isVisible: boolean;
}

export class AIComponent extends BaseComponent {
    public static readonly TYPE = 'ai';
    
    // AI state
    public currentBehavior: AIBehaviorType = AIBehaviorType.IDLE;
    public previousBehavior: AIBehaviorType = AIBehaviorType.IDLE;
    public behaviorChangeTime: number = 0;
    
    // Behaviors
    public behaviors: Map<AIBehaviorType, AIBehavior> = new Map();
    public availableBehaviors: AIBehaviorType[] = [];
    
    // Targeting
    public currentTarget: AITarget | null = null;
    public possibleTargets: Map<string, AITarget> = new Map();
    public targetScanRadius: number = 10;
    public targetScanInterval: number = 0.5; // Seconds between scans
    private lastTargetScan: number = 0;
    
    // Vision and detection
    public viewDistance: number = 8;
    public viewAngle: number = Math.PI / 2; // 90 degrees
    public hearingRadius: number = 5;
    public reactionTime: number = 0.5; // Seconds to react to stimuli
    
    // Movement AI
    public moveSpeed: number = 2;
    public turnSpeed: number = 3; // Radians per second
    public arrivalDistance: number = 0.5; // Distance to consider "arrived" at destination
    public currentDestination: Vector2 | null = null;
    public patrolPoints: Vector2[] = [];
    public currentPatrolIndex: number = 0;
    
    // Combat AI
    public attackRange: number = 1.5;
    public attackCooldown: number = 1.0; // Seconds between attacks
    public lastAttackTime: number = 0;
    public fleeHealthThreshold: number = 0.3; // Flee when health below this percentage
    
    // State memory
    public alertLevel: number = 0; // 0 = calm, 1 = fully alert
    public alertDecayRate: number = 0.5; // How fast alert level decreases
    public memory: Map<string, any> = new Map(); // For storing arbitrary AI data
    
    // Events
    public onBehaviorChanged?: ((newBehavior: AIBehaviorType, oldBehavior: AIBehaviorType) => void) | undefined;
    public onTargetAcquired?: ((target: IGameEntity) => void) | undefined;
    public onTargetLost?: ((target: IGameEntity) => void) | undefined;
    public onAttack?: ((target: IGameEntity) => void) | undefined;
    
    constructor(entity: IGameEntity) {
        super(entity, AIComponent.TYPE);
        
        this.setupDefaultBehaviors();
    }
    
    /**
     * Update AI logic
     */
    public update(deltaTime: number): void {
        if (!this.enabled) {return;}
        
        // Update timing
        this.behaviorChangeTime += deltaTime;
        
        // Scan for targets periodically
        if (performance.now() / 1000 - this.lastTargetScan > this.targetScanInterval) {
            this.scanForTargets();
            this.lastTargetScan = performance.now() / 1000;
        }
        
        // Update alert level
        this.alertLevel = Math.max(0, this.alertLevel - this.alertDecayRate * deltaTime);
        
        // Update target visibility
        this.updateTargetVisibility();
        
        // Choose and execute behavior
        this.selectBehavior();
        this.executeBehavior(deltaTime);
    }
    
    /**
     * Add a behavior to the AI
     */
    public addBehavior(behavior: AIBehavior): void {
        this.behaviors.set(behavior.type, behavior);
        if (!this.availableBehaviors.includes(behavior.type)) {
            this.availableBehaviors.push(behavior.type);
        }
    }
    
    /**
     * Remove a behavior from the AI
     */
    public removeBehavior(type: AIBehaviorType): void {
        this.behaviors.delete(type);
        const index = this.availableBehaviors.indexOf(type);
        if (index !== -1) {
            this.availableBehaviors.splice(index, 1);
        }
    }
    
    /**
     * Force behavior change
     */
    public setBehavior(type: AIBehaviorType): void {
        if (this.currentBehavior === type) {return;}
        
        // Exit current behavior
        const currentBehavior = this.behaviors.get(this.currentBehavior);
        if (currentBehavior?.onExit) {
            currentBehavior.onExit(this);
        }
        
        // Change behavior
        this.previousBehavior = this.currentBehavior;
        this.currentBehavior = type;
        this.behaviorChangeTime = 0;
        
        // Enter new behavior
        const newBehavior = this.behaviors.get(type);
        if (newBehavior?.onEnter) {
            newBehavior.onEnter(this);
        }
        
        this.onBehaviorChanged?.(type, this.previousBehavior);
    }
    
    /**
     * Set current target
     */
    public setTarget(target: IGameEntity | null): void {
        if (target === null) {
            if (this.currentTarget) {
                this.onTargetLost?.(this.currentTarget.entity);
                this.currentTarget = null;
            }
            return;
        }
        
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        const wasNewTarget = this.currentTarget?.entity.id !== target.id;
        
        this.currentTarget = {
            entity: target,
            priority: 1,
            lastSeenPosition: target.position,
            lastSeenTime: performance.now() / 1000,
            isVisible: this.canSeeEntity(target)
        };
        
        if (wasNewTarget) {
            this.onTargetAcquired?.(target);
            this.alertLevel = Math.min(1, this.alertLevel + 0.5);
        }
    }
    
    /**
     * Move towards a position
     */
    public moveTowards(destination: Vector2, deltaTime: number): boolean {
        const transform = this.entity.getComponent<TransformComponent>('transform');
        const input = this.entity.getComponent<InputComponent>('input');
        
        if (!transform || !input) {return false;}
        
        const currentPos = transform.getPosition();
        const direction = {
            x: destination.x - currentPos.x,
            y: destination.y - currentPos.y
        };
        
        const distance = Math.sqrt(direction.x * direction.x + direction.y * direction.y);
        
        // Check if we've arrived
        if (distance <= this.arrivalDistance) {
            input.setAxisValue('horizontal', 0);
            input.setAxisValue('vertical', 0);
            return true;
        }
        
        // Normalize direction
        direction.x /= distance;
        direction.y /= distance;
        
        // Set movement input
        input.setAxisValue('horizontal', direction.x);
        input.setAxisValue('vertical', direction.y);
        
        // Face movement direction
        const targetAngle = Math.atan2(direction.y, direction.x);
        this.faceDirection(targetAngle, deltaTime);
        
        return false;
    }
    
    /**
     * Face a specific direction
     */
    public faceDirection(targetAngle: number, deltaTime: number): void {
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {return;}
        
        const currentAngle = transform.getRotation();
        let angleDiff = targetAngle - currentAngle;
        
        // Normalize angle difference to [-π, π]
        while (angleDiff > Math.PI) {angleDiff -= 2 * Math.PI;}
        while (angleDiff < -Math.PI) {angleDiff += 2 * Math.PI;}
        
        // Apply turn speed
        const maxTurn = this.turnSpeed * deltaTime;
        if (Math.abs(angleDiff) <= maxTurn) {
            transform.setRotation(targetAngle);
        } else {
            const turnDirection = Math.sign(angleDiff);
            transform.setRotation(currentAngle + turnDirection * maxTurn);
        }
    }
    
    /**
     * Check if AI can see an entity
     */
    public canSeeEntity(target: IGameEntity): boolean {
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {return false;}
        
        const myPos = transform.getWorldPosition();
        const targetPos = target.position;
        
        // Check distance
        const distance = Math.sqrt(
            (targetPos.x - myPos.x) ** 2 + (targetPos.y - myPos.y) ** 2
        );
        
        if (distance > this.viewDistance) {return false;}
        
        // Check angle
        const toTarget = Math.atan2(targetPos.y - myPos.y, targetPos.x - myPos.x);
        const myRotation = transform.getWorldRotation();
        let angleDiff = Math.abs(toTarget - myRotation);
        
        // Normalize angle difference
        while (angleDiff > Math.PI) {angleDiff = 2 * Math.PI - angleDiff;}
        
        return angleDiff <= this.viewAngle / 2;
    }
    
    /**
     * Attack current target
     */
    public attackTarget(): boolean {
        if (!this.currentTarget || !this.currentTarget.isVisible) {return false;}
        
        const currentTime = performance.now() / 1000;
        if (currentTime - this.lastAttackTime < this.attackCooldown) {return false;}
        
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (!transform) {return false;}
        
        const distance = transform.distanceTo(
            this.currentTarget.entity.getComponent<TransformComponent>('transform')!
        );
        
        if (distance <= this.attackRange) {
            this.lastAttackTime = currentTime;
            this.onAttack?.(this.currentTarget.entity);
            return true;
        }
        
        return false;
    }
    
    /**
     * Scan for targets in range
     */
    private scanForTargets(): void {
        // This would be implemented by getting entities from entity manager
        // For now, we'll just update existing targets
        this.updateTargetVisibility();
    }
    
    /**
     * Update visibility of known targets
     */
    private updateTargetVisibility(): void {
        if (this.currentTarget) {
            const wasVisible = this.currentTarget.isVisible;
            this.currentTarget.isVisible = this.canSeeEntity(this.currentTarget.entity);
            
            if (this.currentTarget.isVisible) {
                this.currentTarget.lastSeenPosition = { ...this.currentTarget.entity.position };
                this.currentTarget.lastSeenTime = performance.now() / 1000;
            } else if (wasVisible) {
                // Just lost sight of target
                this.alertLevel = Math.max(0.7, this.alertLevel);
            }
        }
    }
    
    /**
     * Select best behavior based on current conditions
     */
    private selectBehavior(): void {
        let bestBehavior: AIBehaviorType = AIBehaviorType.IDLE;
        let highestPriority = -1;
        
        for (const behaviorType of this.availableBehaviors) {
            const behavior = this.behaviors.get(behaviorType);
            if (behavior && behavior.canActivate(this) && behavior.priority > highestPriority) {
                bestBehavior = behaviorType;
                highestPriority = behavior.priority;
            }
        }
        
        if (bestBehavior !== this.currentBehavior) {
            this.setBehavior(bestBehavior);
        }
    }
    
    /**
     * Execute current behavior
     */
    private executeBehavior(deltaTime: number): void {
        const behavior = this.behaviors.get(this.currentBehavior);
        if (behavior) {
            behavior.execute(this, deltaTime);
        }
    }
    
    /**
     * Setup default AI behaviors
     */
    private setupDefaultBehaviors(): void {
        // Idle behavior
        this.addBehavior({
            type: AIBehaviorType.IDLE,
            priority: 0,
            canActivate: () => true,
            execute: (ai, deltaTime) => {
                // Do nothing, just exist
                const input = ai.entity.getComponent<InputComponent>('input');
                if (input) {
                    input.setAxisValue('horizontal', 0);
                    input.setAxisValue('vertical', 0);
                }
            }
        });
        
        // Chase behavior
        this.addBehavior({
            type: AIBehaviorType.CHASE,
            priority: 5,
            canActivate: (ai) => ai.currentTarget !== null && ai.currentTarget.isVisible,
            execute: (ai, deltaTime) => {
                if (ai.currentTarget) {
                    ai.moveTowards(ai.currentTarget.entity.position, deltaTime);
                }
            }
        });
        
        // Attack behavior
        this.addBehavior({
            type: AIBehaviorType.ATTACK,
            priority: 10,
            canActivate: (ai) => {
                if (!ai.currentTarget || !ai.currentTarget.isVisible) {return false;}
                
                const transform = ai.entity.getComponent<TransformComponent>('transform');
                if (!transform) {return false;}
                
                const targetTransform = ai.currentTarget.entity.getComponent<TransformComponent>('transform');
                if (!targetTransform) {return false;}
                
                return transform.distanceTo(targetTransform) <= ai.attackRange;
            },
            execute: (ai, deltaTime) => {
                // Face target and attack
                if (ai.currentTarget) {
                    const targetPos = ai.currentTarget.entity.position;
                    const transform = ai.entity.getComponent<TransformComponent>('transform');
                    if (transform) {
                        const angle = Math.atan2(
                            targetPos.y - transform.getPosition().y,
                            targetPos.x - transform.getPosition().x
                        );
                        ai.faceDirection(angle, deltaTime);
                    }
                    
                    ai.attackTarget();
                }
            }
        });
    }
    
    /**
     * Serialize AI component data
     */
    public override serialize(): Record<string, unknown> {
        return {
            ...super.serialize(),
            currentBehavior: this.currentBehavior,
            viewDistance: this.viewDistance,
            viewAngle: this.viewAngle,
            hearingRadius: this.hearingRadius,
            moveSpeed: this.moveSpeed,
            turnSpeed: this.turnSpeed,
            attackRange: this.attackRange,
            attackCooldown: this.attackCooldown,
            fleeHealthThreshold: this.fleeHealthThreshold,
            alertLevel: this.alertLevel,
            patrolPoints: this.patrolPoints,
            currentPatrolIndex: this.currentPatrolIndex
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Clear event handlers
        this.onBehaviorChanged = undefined;
        this.onTargetAcquired = undefined;
        this.onTargetLost = undefined;
        this.onAttack = undefined;
        
        // Clear collections
        this.behaviors.clear();
        this.possibleTargets.clear();
        this.memory.clear();
        this.patrolPoints.length = 0;
        
        // Clear target
        this.currentTarget = null;
    }
}
