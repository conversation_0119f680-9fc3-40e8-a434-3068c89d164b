/**
 * TransformComponent - Handles entity position, rotation, and scale
 * Core component for spatial representation in the game world
 */

import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity, Vector2 } from '@/types';

export class TransformComponent extends BaseComponent {
    public static readonly TYPE = 'transform';
    
    // Local transform properties (relative to parent)
    public localPosition: Vector2;
    public localRotation: number;
    public localScale: Vector2;
    
    // World transform properties (computed)
    public worldPosition: Vector2;
    public worldRotation: number;
    public worldScale: Vector2;
    
    // Parent-child relationships
    public parent: TransformComponent | null = null;
    public children: TransformComponent[] = [];
    
    // Dirty flag to optimize transform calculations
    private isDirty: boolean = true;
    
    constructor(entity: IGameEntity, position: Vector2 = { x: 0, y: 0 }) {
        super(entity, TransformComponent.TYPE);
        
        this.localPosition = { ...position };
        this.localRotation = 0;
        this.localScale = { x: 1, y: 1 };
        
        this.worldPosition = { ...position };
        this.worldRotation = 0;
        this.worldScale = { x: 1, y: 1 };
    }
    
    /**
     * Update transform calculations
     */
    public update(deltaTime: number): void {
        if (this.isDirty || (this.parent && this.parent.isDirty)) {
            this.updateWorldTransform();
            this.isDirty = false;
        }
    }
    
    /**
     * Set local position
     */
    public setPosition(x: number, y: number): void {
        this.localPosition.x = x;
        this.localPosition.y = y;
        this.markDirty();
        
        // Update entity position for backwards compatibility
        this.entity.position.x = x;
        this.entity.position.y = y;
    }
    
    /**
     * Get local position
     */
    public getPosition(): Vector2 {
        return { ...this.localPosition };
    }
    
    /**
     * Translate by offset
     */
    public translate(dx: number, dy: number): void {
        this.localPosition.x += dx;
        this.localPosition.y += dy;
        this.markDirty();
        
        // Update entity position
        this.entity.position.x = this.localPosition.x;
        this.entity.position.y = this.localPosition.y;
    }
    
    /**
     * Set local rotation in radians
     */
    public setRotation(rotation: number): void {
        this.localRotation = rotation;
        this.markDirty();
        
        // Update entity rotation
        this.entity.rotation = rotation;
    }
    
    /**
     * Get local rotation
     */
    public getRotation(): number {
        return this.localRotation;
    }
    
    /**
     * Rotate by angle in radians
     */
    public rotate(angle: number): void {
        this.localRotation += angle;
        this.markDirty();
        
        // Update entity rotation
        this.entity.rotation = this.localRotation;
    }
    
    /**
     * Set local scale
     */
    public setScale(x: number, y: number = x): void {
        this.localScale.x = x;
        this.localScale.y = y;
        this.markDirty();
        
        // Update entity scale
        this.entity.scale.x = x;
        this.entity.scale.y = y;
    }
    
    /**
     * Get local scale
     */
    public getScale(): Vector2 {
        return { ...this.localScale };
    }

    /**
     * Property accessors for backward compatibility with systems
     * These delegate to the existing methods while providing direct property access
     */

    /**
     * Position property accessor (delegates to getPosition/setPosition)
     */
    public get position(): Vector2 {
        return this.getPosition();
    }

    public set position(value: Vector2) {
        this.setPosition(value.x, value.y);
    }

    /**
     * Rotation property accessor (delegates to getRotation/setRotation)
     */
    public get rotation(): number {
        return this.getRotation();
    }

    public set rotation(value: number) {
        this.setRotation(value);
    }

    /**
     * Scale property accessor (delegates to getScale/setScale)
     */
    public get scale(): Vector2 {
        return this.getScale();
    }

    public set scale(value: Vector2) {
        this.setScale(value.x, value.y);
    }
    
    /**
     * Set parent transform
     */
    public setParent(parent: TransformComponent | null): void {
        // Remove from current parent
        if (this.parent) {
            const index = this.parent.children.indexOf(this);
            if (index !== -1) {
                this.parent.children.splice(index, 1);
            }
        }
        
        // Set new parent
        this.parent = parent;
        if (parent) {
            parent.children.push(this);
        }
        
        this.markDirty();
    }
    
    /**
     * Add child transform
     */
    public addChild(child: TransformComponent): void {
        child.setParent(this);
    }
    
    /**
     * Remove child transform
     */
    public removeChild(child: TransformComponent): void {
        const index = this.children.indexOf(child);
        if (index !== -1) {
            this.children.splice(index, 1);
            child.parent = null;
            child.markDirty();
        }
    }
    
    /**
     * Get world position
     */
    public getWorldPosition(): Vector2 {
        if (this.isDirty) {
            this.updateWorldTransform();
        }
        return { ...this.worldPosition };
    }
    
    /**
     * Get world rotation
     */
    public getWorldRotation(): number {
        if (this.isDirty) {
            this.updateWorldTransform();
        }
        return this.worldRotation;
    }
    
    /**
     * Get world scale
     */
    public getWorldScale(): Vector2 {
        if (this.isDirty) {
            this.updateWorldTransform();
        }
        return { ...this.worldScale };
    }
    
    /**
     * Get forward direction vector (based on rotation)
     */
    public getForward(): Vector2 {
        return {
            x: Math.cos(this.worldRotation),
            y: Math.sin(this.worldRotation)
        };
    }
    
    /**
     * Get right direction vector (based on rotation)
     */
    public getRight(): Vector2 {
        return {
            x: Math.cos(this.worldRotation + Math.PI / 2),
            y: Math.sin(this.worldRotation + Math.PI / 2)
        };
    }
    
    /**
     * Look at a target position
     */
    public lookAt(target: Vector2): void {
        const direction = {
            x: target.x - this.worldPosition.x,
            y: target.y - this.worldPosition.y
        };
        
        const angle = Math.atan2(direction.y, direction.x);
        this.setRotation(angle);
    }
    
    /**
     * Calculate distance to another transform
     */
    public distanceTo(other: TransformComponent): number {
        const dx = this.worldPosition.x - other.worldPosition.x;
        const dy = this.worldPosition.y - other.worldPosition.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Mark transform as dirty (needs recalculation)
     */
    private markDirty(): void {
        this.isDirty = true;
        
        // Mark all children as dirty too
        for (const child of this.children) {
            child.markDirty();
        }
    }
    
    /**
     * Update world transform based on parent hierarchy
     */
    private updateWorldTransform(): void {
        if (this.parent) {
            // Apply parent transformation
            const parentWorld = this.parent.getWorldPosition();
            const parentRotation = this.parent.getWorldRotation();
            const parentScale = this.parent.getWorldScale();
            
            // Rotate local position by parent rotation
            const cos = Math.cos(parentRotation);
            const sin = Math.sin(parentRotation);
            
            this.worldPosition.x = parentWorld.x + 
                (this.localPosition.x * cos - this.localPosition.y * sin) * parentScale.x;
            this.worldPosition.y = parentWorld.y + 
                (this.localPosition.x * sin + this.localPosition.y * cos) * parentScale.y;
            
            this.worldRotation = parentRotation + this.localRotation;
            this.worldScale.x = parentScale.x * this.localScale.x;
            this.worldScale.y = parentScale.y * this.localScale.y;
        } else {
            // No parent, world transform equals local transform
            this.worldPosition.x = this.localPosition.x;
            this.worldPosition.y = this.localPosition.y;
            this.worldRotation = this.localRotation;
            this.worldScale.x = this.localScale.x;
            this.worldScale.y = this.localScale.y;
        }
    }
    
    /**
     * Serialize transform data
     */
    public override serialize(): Record<string, unknown> {
        return {
            ...super.serialize(),
            localPosition: this.localPosition,
            localRotation: this.localRotation,
            localScale: this.localScale,
            parentId: this.parent?.entity.id || null
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Remove from parent
        if (this.parent) {
            this.parent.removeChild(this);
        }
        
        // Reparent children to this transform's parent
        for (const child of [...this.children]) {
            child.setParent(this.parent);
        }
    }
}
