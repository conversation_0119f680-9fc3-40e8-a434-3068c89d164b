/**
 * HealthComponent - Manages entity health, damage, and death
 * Handles HP, shields, damage immunity, and death events
 */

import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity } from '@/types';

export interface DamageInfo {
    amount: number;
    source?: IGameEntity;
    damageType?: string;
    isHealing?: boolean;
    ignoreDamageReduction?: boolean;
    ignoreImmunity?: boolean;
}

export class HealthComponent extends BaseComponent {
    public static readonly TYPE = 'health';
    
    // Health properties
    public maxHealth: number;
    public currentHealth: number;
    public healthRegenRate: number = 0; // HP per second
    public healthRegenDelay: number = 3; // Seconds after taking damage before regen starts
    
    // Shield properties
    public maxShield: number = 0;
    public currentShield: number = 0;
    public shieldRegenRate: number = 0; // Shield per second
    public shieldRegenDelay: number = 2; // Seconds after taking damage before shield regen starts
    
    // Damage properties
    public damageReduction: number = 0; // Percentage (0-1)
    public isInvulnerable: boolean = false;
    public isDead: boolean = false;
    
    // Immunity properties
    public immunityDuration: number = 0; // Seconds of immunity after taking damage
    public currentImmunityTime: number = 0;
    public immuneToDamageTypes: Set<string> = new Set();
    
    // Regeneration tracking
    private lastDamageTime: number = 0;
    private lastShieldDamageTime: number = 0;
    
    // Events
    public onHealthChanged?: ((currentHealth: number, maxHealth: number, delta: number) => void) | undefined;
    public onShieldChanged?: ((currentShield: number, maxShield: number, delta: number) => void) | undefined;
    public onDamageTaken?: ((damage: number, source?: IGameEntity) => void) | undefined;
    public onHealing?: ((healing: number, source?: IGameEntity) => void) | undefined;
    public onDeath?: ((killer?: IGameEntity) => void) | undefined;
    public onRevive?: (() => void) | undefined;
    
    constructor(entity: IGameEntity, maxHealth: number = 100) {
        super(entity, HealthComponent.TYPE);
        
        this.maxHealth = maxHealth;
        this.currentHealth = maxHealth;
    }
    
    /**
     * Update health regeneration and immunity
     */
    public update(deltaTime: number): void {
        if (!this.enabled) {return;}
        
        const currentTime = performance.now() / 1000;
        
        // Update immunity timer
        if (this.currentImmunityTime > 0) {
            this.currentImmunityTime -= deltaTime;
            if (this.currentImmunityTime <= 0) {
                this.currentImmunityTime = 0;
            }
        }
        
        // Health regeneration
        if (this.healthRegenRate > 0 && 
            !this.isDead && 
            this.currentHealth < this.maxHealth &&
            currentTime - this.lastDamageTime >= this.healthRegenDelay) {
            
            const healing = this.healthRegenRate * deltaTime;
            this.heal(healing);
        }
        
        // Shield regeneration
        if (this.shieldRegenRate > 0 && 
            this.currentShield < this.maxShield &&
            currentTime - this.lastShieldDamageTime >= this.shieldRegenDelay) {
            
            const shieldHealing = this.shieldRegenRate * deltaTime;
            this.addShield(shieldHealing);
        }
    }
    
    /**
     * Take damage (accepts both number and DamageInfo)
     */
    public takeDamage(damageOrInfo: number | DamageInfo): boolean {
        // Convert number to DamageInfo if needed
        const damageInfo: DamageInfo = typeof damageOrInfo === 'number'
            ? { amount: damageOrInfo }
            : damageOrInfo;
        if (this.isDead || !this.enabled) {return false;}
        
        // Check invulnerability
        if (this.isInvulnerable && !damageInfo.ignoreImmunity) {return false;}
        
        // Check immunity timer
        if (this.currentImmunityTime > 0 && !damageInfo.ignoreImmunity) {return false;}
        
        // Check damage type immunity
        if (damageInfo.damageType && 
            this.immuneToDamageTypes.has(damageInfo.damageType) && 
            !damageInfo.ignoreImmunity) {
            return false;
        }
        
        let damage = damageInfo.amount;
        
        // Apply damage reduction
        if (!damageInfo.ignoreDamageReduction) {
            damage *= (1 - this.damageReduction);
        }
        
        damage = Math.max(0, damage);
        
        // Apply damage to shield first
        if (this.currentShield > 0) {
            const shieldDamage = Math.min(damage, this.currentShield);
            this.currentShield -= shieldDamage;
            damage -= shieldDamage;
            
            this.lastShieldDamageTime = performance.now() / 1000;
            this.onShieldChanged?.(this.currentShield, this.maxShield, -shieldDamage);
        }
        
        // Apply remaining damage to health
        if (damage > 0) {
            const oldHealth = this.currentHealth;
            this.currentHealth = Math.max(0, this.currentHealth - damage);
            const actualDamage = oldHealth - this.currentHealth;
            
            this.lastDamageTime = performance.now() / 1000;
            
            // Start immunity period
            if (this.immunityDuration > 0) {
                this.currentImmunityTime = this.immunityDuration;
            }
            
            // Trigger events
            this.onDamageTaken?.(actualDamage, damageInfo.source);
            this.onHealthChanged?.(this.currentHealth, this.maxHealth, -actualDamage);
            
            // Check for death
            if (this.currentHealth <= 0 && !this.isDead) {
                this.die(damageInfo.source);
            }
        }
        
        return true;
    }
    
    /**
     * Heal the entity
     */
    public heal(amount: number, source?: IGameEntity): number {
        if (this.isDead || !this.enabled || amount <= 0) {return 0;}
        
        const oldHealth = this.currentHealth;
        this.currentHealth = Math.min(this.maxHealth, this.currentHealth + amount);
        const actualHealing = this.currentHealth - oldHealth;
        
        if (actualHealing > 0) {
            this.onHealing?.(actualHealing, source);
            this.onHealthChanged?.(this.currentHealth, this.maxHealth, actualHealing);
        }
        
        return actualHealing;
    }
    
    /**
     * Add shield
     */
    public addShield(amount: number): number {
        if (!this.enabled || amount <= 0) {return 0;}
        
        const oldShield = this.currentShield;
        this.currentShield = Math.min(this.maxShield, this.currentShield + amount);
        const actualShieldGain = this.currentShield - oldShield;
        
        if (actualShieldGain > 0) {
            this.onShieldChanged?.(this.currentShield, this.maxShield, actualShieldGain);
        }
        
        return actualShieldGain;
    }
    
    /**
     * Set maximum health
     */
    public setMaxHealth(maxHealth: number, adjustCurrent: boolean = false): void {
        this.maxHealth = Math.max(1, maxHealth);
        
        if (adjustCurrent) {
            const healthPercentage = this.currentHealth / this.maxHealth;
            this.currentHealth = this.maxHealth * healthPercentage;
        } else {
            this.currentHealth = Math.min(this.currentHealth, this.maxHealth);
        }
        
        this.onHealthChanged?.(this.currentHealth, this.maxHealth, 0);
    }
    
    /**
     * Set maximum shield
     */
    public setMaxShield(maxShield: number, adjustCurrent: boolean = false): void {
        this.maxShield = Math.max(0, maxShield);
        
        if (adjustCurrent) {
            const shieldPercentage = this.maxShield > 0 ? this.currentShield / this.maxShield : 0;
            this.currentShield = this.maxShield * shieldPercentage;
        } else {
            this.currentShield = Math.min(this.currentShield, this.maxShield);
        }
        
        this.onShieldChanged?.(this.currentShield, this.maxShield, 0);
    }
    
    /**
     * Kill the entity instantly
     */
    public kill(killer?: IGameEntity): void {
        if (this.isDead) {return;}
        
        this.currentHealth = 0;
        this.die(killer);
    }
    
    /**
     * Revive the entity
     */
    public revive(healthPercentage: number = 1.0): void {
        if (!this.isDead) {return;}
        
        this.isDead = false;
        this.currentHealth = Math.max(1, this.maxHealth * Math.max(0, Math.min(1, healthPercentage)));
        this.currentShield = 0;
        this.entity.active = true;
        
        this.onRevive?.();
        this.onHealthChanged?.(this.currentHealth, this.maxHealth, this.currentHealth);
    }
    
    /**
     * Set damage immunity for specific damage types
     */
    public setDamageTypeImmunity(damageType: string, immune: boolean): void {
        if (immune) {
            this.immuneToDamageTypes.add(damageType);
        } else {
            this.immuneToDamageTypes.delete(damageType);
        }
    }
    
    /**
     * Get health as percentage (0-1)
     */
    public getHealthPercentage(): number {
        return this.maxHealth > 0 ? this.currentHealth / this.maxHealth : 0;
    }
    
    /**
     * Get shield as percentage (0-1)
     */
    public getShieldPercentage(): number {
        return this.maxShield > 0 ? this.currentShield / this.maxShield : 0;
    }
    
    /**
     * Get total effective health (health + shield)
     */
    public getTotalHealth(): number {
        return this.currentHealth + this.currentShield;
    }
    
    /**
     * Get total maximum effective health
     */
    public getTotalMaxHealth(): number {
        return this.maxHealth + this.maxShield;
    }
    
    /**
     * Check if entity is at full health (including shield)
     */
    public isFullHealth(): boolean {
        return this.currentHealth >= this.maxHealth && this.currentShield >= this.maxShield;
    }
    
    /**
     * Check if entity has any shield
     */
    public hasShield(): boolean {
        return this.currentShield > 0;
    }
    
    /**
     * Check if entity is currently immune to damage
     */
    public isImmune(): boolean {
        return this.isInvulnerable || this.currentImmunityTime > 0;
    }
    
    /**
     * Handle death
     */
    private die(killer?: IGameEntity): void {
        this.isDead = true;
        this.currentHealth = 0;
        
        // Disable the entity (systems can check this)
        this.entity.active = false;
        
        this.onDeath?.(killer);
    }
    
    /**
     * Serialize health component data
     */
    public override serialize(): Record<string, unknown> {
        return {
            ...super.serialize(),
            maxHealth: this.maxHealth,
            currentHealth: this.currentHealth,
            healthRegenRate: this.healthRegenRate,
            healthRegenDelay: this.healthRegenDelay,
            maxShield: this.maxShield,
            currentShield: this.currentShield,
            shieldRegenRate: this.shieldRegenRate,
            shieldRegenDelay: this.shieldRegenDelay,
            damageReduction: this.damageReduction,
            isInvulnerable: this.isInvulnerable,
            isDead: this.isDead,
            immunityDuration: this.immunityDuration,
            immuneToDamageTypes: Array.from(this.immuneToDamageTypes)
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Clear event handlers
        this.onHealthChanged = undefined;
        this.onShieldChanged = undefined;
        this.onDamageTaken = undefined;
        this.onHealing = undefined;
        this.onDeath = undefined;
        this.onRevive = undefined;
        
        // Clear immunity set
        this.immuneToDamageTypes.clear();
    }
}
