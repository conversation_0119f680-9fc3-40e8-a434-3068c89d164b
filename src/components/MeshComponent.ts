/**
 * MeshComponent - <PERSON>les 3D mesh rendering for entities
 * Manages Three.js mesh objects and their visual properties
 */

import * as THREE from 'three';
import { BaseComponent } from '@/core/BaseComponent';
import type { IGameEntity, IRenderer } from '@/types';
import { TransformComponent } from './TransformComponent';

export class MeshComponent extends BaseComponent {
    public static readonly TYPE = 'mesh';
    
    public mesh: THREE.Mesh;
    public geometry: THREE.BufferGeometry;
    public material: THREE.Material;
    
    // Rendering properties
    public visible: boolean = true;
    public castShadows: boolean = true;
    public receiveShadows: boolean = true;
    public renderOrder: number = 0;
    
    // Animation properties
    public animationMixer: THREE.AnimationMixer | null = null;
    public animations: Map<string, THREE.AnimationClip> = new Map();
    public currentAnimation: string | null = null;
    
    // Color and transparency
    private originalColor: THREE.Color | null = null;
    private colorTint: THREE.Color = new THREE.Color(1, 1, 1);
    private opacity: number = 1;

    // Properties expected by systems for backward compatibility
    public color: { r: number; g: number; b: number; a: number } = { r: 1, g: 1, b: 1, a: 1 };
    public size: { x: number; y: number; z: number } = { x: 1, y: 1, z: 1 };
    public primitiveType: 'box' | 'sphere' | 'plane' | 'cylinder' = 'box';
    public materialType: 'basic' | 'lambert' | 'phong' | 'standard' = 'basic';
    
    constructor(
        entity: IGameEntity,
        geometry?: THREE.BufferGeometry,
        material?: THREE.Material
    ) {
        super(entity, MeshComponent.TYPE);

        // Use provided geometry/material or create defaults
        this.geometry = geometry || new THREE.BoxGeometry(1, 1, 1);
        this.material = material || new THREE.MeshBasicMaterial({ color: 0xffffff });
        this.mesh = new THREE.Mesh(this.geometry, this.material);

        // Store original color for tinting
        if (this.material instanceof THREE.MeshBasicMaterial ||
            this.material instanceof THREE.MeshLambertMaterial ||
            this.material instanceof THREE.MeshPhongMaterial ||
            this.material instanceof THREE.MeshStandardMaterial) {
            this.originalColor = this.material.color.clone();

            // Initialize color property from material
            this.color = {
                r: this.material.color.r,
                g: this.material.color.g,
                b: this.material.color.b,
                a: this.material.opacity || 1
            };
        }

        // Set up mesh properties
        this.mesh.castShadow = this.castShadows;
        this.mesh.receiveShadow = this.receiveShadows;
        this.mesh.renderOrder = this.renderOrder;

        // Link mesh to entity ID for debugging
        this.mesh.userData = { entityId: entity.id };
    }
    
    /**
     * Update mesh transform and animations
     */
    public update(deltaTime: number): void {
        if (!this.enabled) {return;}
        
        // Update transform from TransformComponent if available
        const transform = this.entity.getComponent<TransformComponent>('transform');
        if (transform) {
            const worldPos = transform.getWorldPosition();
            const worldRot = transform.getWorldRotation();
            const worldScale = transform.getWorldScale();
            
            this.mesh.position.set(worldPos.x, 0, worldPos.y); // 2.5D: Y is up, Z is forward/back
            this.mesh.rotation.y = worldRot; // Rotate around Y axis
            this.mesh.scale.set(worldScale.x, 1, worldScale.y);
        }
        
        // Update animations
        if (this.animationMixer) {
            this.animationMixer.update(deltaTime);
        }
        
        // Update visibility
        this.mesh.visible = this.visible && this.entity.active;
    }
    
    /**
     * Set the geometry of the mesh
     */
    public setGeometry(geometry: THREE.BufferGeometry): void {
        this.geometry.dispose(); // Cleanup old geometry
        this.geometry = geometry;
        this.mesh.geometry = geometry;
    }
    
    /**
     * Set the material of the mesh
     */
    public setMaterial(material: THREE.Material): void {
        this.material.dispose(); // Cleanup old material
        this.material = material;
        this.mesh.material = material;
        
        // Update original color
        if (material instanceof THREE.MeshBasicMaterial || 
            material instanceof THREE.MeshLambertMaterial ||
            material instanceof THREE.MeshPhongMaterial ||
            material instanceof THREE.MeshStandardMaterial) {
            this.originalColor = material.color.clone();
        }
    }
    
    /**
     * Set mesh visibility
     */
    public setVisible(visible: boolean): void {
        this.visible = visible;
        this.mesh.visible = visible && this.entity.active;
    }
    
    /**
     * Set color tint (multiplied with original color)
     */
    public setColorTint(r: number, g: number, b: number): void {
        this.colorTint.setRGB(r, g, b);
        this.updateMaterialColor();
    }
    
    /**
     * Set opacity
     */
    public setOpacity(opacity: number): void {
        this.opacity = Math.max(0, Math.min(1, opacity));

        if (this.material instanceof THREE.MeshBasicMaterial ||
            this.material instanceof THREE.MeshLambertMaterial ||
            this.material instanceof THREE.MeshPhongMaterial ||
            this.material instanceof THREE.MeshStandardMaterial) {
            this.material.opacity = this.opacity;
            this.material.transparent = this.opacity < 1;
        }
    }

    /**
     * Update material color from color property and tint
     */
    public updateMaterialColor(): void {
        if (this.material instanceof THREE.MeshBasicMaterial ||
            this.material instanceof THREE.MeshLambertMaterial ||
            this.material instanceof THREE.MeshPhongMaterial ||
            this.material instanceof THREE.MeshStandardMaterial) {

            // Set base color from color property
            this.material.color.setRGB(this.color.r, this.color.g, this.color.b);

            // Apply tint if original color exists
            if (this.originalColor) {
                this.material.color.multiply(this.colorTint);
            }

            this.material.opacity = this.color.a;
            this.material.transparent = this.color.a < 1.0;
        }
    }

    /**
     * Update geometry scale from size property
     */
    public updateGeometryScale(): void {
        this.mesh.scale.set(this.size.x, this.size.y, this.size.z);
    }
    
    /**
     * Flash effect (quickly change color and return)
     */
    public flash(color: THREE.Color, duration: number = 0.1): void {
        const originalTint = this.colorTint.clone();
        this.setColorTint(color.r, color.g, color.b);
        
        setTimeout(() => {
            this.setColorTint(originalTint.r, originalTint.g, originalTint.b);
        }, duration * 1000);
    }
    
    /**
     * Add animation clip
     */
    public addAnimation(name: string, clip: THREE.AnimationClip): void {
        this.animations.set(name, clip);
        
        if (!this.animationMixer) {
            this.animationMixer = new THREE.AnimationMixer(this.mesh);
        }
    }
    
    /**
     * Play animation
     */
    public playAnimation(name: string, loop: boolean = true, fadeTime: number = 0.2): void {
        const clip = this.animations.get(name);
        if (!clip || !this.animationMixer) {return;}
        
        // Stop current animation if any
        if (this.currentAnimation) {
            this.stopAnimation(fadeTime);
        }
        
        const action = this.animationMixer.clipAction(clip);
        action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, Infinity);
        
        if (fadeTime > 0) {
            action.fadeIn(fadeTime);
        }
        
        action.play();
        this.currentAnimation = name;
    }
    
    /**
     * Stop current animation
     */
    public stopAnimation(fadeTime: number = 0.2): void {
        if (!this.animationMixer || !this.currentAnimation) {return;}
        
        const clip = this.animations.get(this.currentAnimation);
        if (clip) {
            const action = this.animationMixer.clipAction(clip);
            
            if (fadeTime > 0) {
                action.fadeOut(fadeTime);
            } else {
                action.stop();
            }
        }
        
        this.currentAnimation = null;
    }
    
    /**
     * Set shadow casting
     */
    public setCastShadows(cast: boolean): void {
        this.castShadows = cast;
        this.mesh.castShadow = cast;
    }
    
    /**
     * Set shadow receiving
     */
    public setReceiveShadows(receive: boolean): void {
        this.receiveShadows = receive;
        this.mesh.receiveShadow = receive;
    }
    
    /**
     * Set render order (for controlling draw order)
     */
    public setRenderOrder(order: number): void {
        this.renderOrder = order;
        this.mesh.renderOrder = order;
    }
    
    /**
     * Get mesh bounding box
     */
    public getBoundingBox(): THREE.Box3 {
        const box = new THREE.Box3();
        box.setFromObject(this.mesh);
        return box;
    }
    
    /**
     * Get mesh bounding sphere
     */
    public getBoundingSphere(): THREE.Sphere {
        const sphere = new THREE.Sphere();
        this.geometry.computeBoundingSphere();
        sphere.copy(this.geometry.boundingSphere!);
        sphere.applyMatrix4(this.mesh.matrixWorld);
        return sphere;
    }
    

    
    /**
     * Serialize mesh component data
     */
    public override serialize(): Record<string, unknown> {
        return {
            ...super.serialize(),
            visible: this.visible,
            castShadows: this.castShadows,
            receiveShadows: this.receiveShadows,
            renderOrder: this.renderOrder,
            colorTint: this.colorTint.toArray(),
            opacity: this.opacity,
            currentAnimation: this.currentAnimation
        };
    }
    
    /**
     * Cleanup when component is destroyed
     */
    public override destroy(): void {
        // Stop animations
        if (this.animationMixer) {
            this.animationMixer.stopAllAction();
        }
        
        // Dispose geometry and material
        this.geometry.dispose();
        this.material.dispose();
        
        // Remove mesh from scene (will be handled by render system)
        this.mesh.removeFromParent();
    }
}
