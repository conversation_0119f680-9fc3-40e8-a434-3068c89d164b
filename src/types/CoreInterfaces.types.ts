/**
 * Core interfaces for Playtime Protocol: Innocence Lost
 * Entity Component System (ECS) architecture definitions
 *
 * @fileoverview Foundational interfaces for the game engine
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// ============================================================================
// CORE TYPE DEFINITIONS
// ============================================================================

/**
 * Simple 2D vector for game coordinates
 */
export interface Vector2 {
    x: number;
    y: number;
}

// ============================================================================
// CORE ECS INTERFACES
// ============================================================================

/**
 * Base game entity interface - represents any object in the game world
 */
export interface IGameEntity {
    /** Unique identifier for the entity */
    readonly id: string;

    /** Entity type identifier (e.g., 'player', 'enemy', 'bullet') */
    readonly type: string;

    /** World position of the entity */
    position: Vector2;

    /** Rotation in radians */
    rotation: number;

    /** Scale factor for rendering */
    scale: Vector2;

    /** Whether the entity is active and should be processed */
    active: boolean;

    /** Collection of components attached to this entity */
    readonly components: Map<string, IComponent>;

    /**
     * Add a component to this entity
     */
    addComponent<T extends IComponent>(component: T): void;

    /**
     * Remove a component by type name
     */
    removeComponent(type: string): void;

    /**
     * Get a component by type name
     */
    getComponent<T extends IComponent>(type: string): T | null;

    /**
     * Check if entity has a specific component type
     */
    hasComponent(type: string): boolean;

    /**
     * Get entity type
     */
    getType(): string;

    /**
     * Cleanup and destroy the entity
     */
    destroy(): void;
}

/**
 * Base component interface - defines behavior and data for entities
 */
export interface IComponent {
    /** Reference to the entity this component is attached to */
    readonly entity: IGameEntity;

    /** Component type identifier */
    readonly type: string;

    /** Whether this component is enabled */
    enabled: boolean;

    /**
     * Update component logic each frame
     * @param deltaTime Time elapsed since last frame in seconds
     */
    update(deltaTime: number): void;

    /**
     * Optional render method for components that need custom rendering
     * @param renderer The game renderer
     */
    render?(renderer: IRenderer): void;

    /**
     * Optional cleanup method called when component is removed
     */
    destroy?(): void;

    /**
     * Serialize component data for save/load
     */
    serialize?(): Record<string, any>;
}

/**
 * Base system interface - processes entities with specific components
 */
export interface ISystem {
    /** System name identifier */
    readonly name: string;

    /** Execution priority (lower number = higher priority) */
    readonly priority: number;

    /** Whether this system is enabled */
    enabled: boolean;

    /**
     * Initialize the system
     */
    initialize?(): Promise<void>;

    /**
     * Update system logic each frame
     * @param entities All game entities
     * @param deltaTime Time elapsed since last frame in seconds
     */
    update(entities: IGameEntity[], deltaTime: number): void;

    /**
     * Cleanup system resources
     */
    cleanup?(): void;

    /**
     * Get system status for debugging
     */
    getStatus?(): SystemStatus;
}

// ============================================================================
// RENDERING INTERFACES
// ============================================================================

/**
 * Main renderer interface
 */
export interface IRenderer {
    /** Initialize the renderer */
    initialize(canvas: HTMLCanvasElement): Promise<void>;

    /** Render a frame */
    render(scene: IScene, camera: ICamera): void;

    /** Cleanup renderer resources */
    destroy(): void;

    /** Get renderer statistics */
    getStats(): RendererStats;
}

/**
 * Scene management interface
 */
export interface IScene {
    /** Add an object to the scene */
    add(object: any): void;

    /** Remove an object from the scene */
    remove(object: any): void;

    /** Clear all objects */
    clear(): void;
}

/**
 * Camera interface
 */
export interface ICamera {
    /** Camera position */
    position: Vector2;

    /** Camera zoom level */
    zoom: number;

    /** Update camera matrices */
    updateMatrices(): void;
}

// ============================================================================
// INPUT INTERFACES
// ============================================================================

/**
 * Input handler function type
 */
export type InputHandler = (event: InputEvent) => void;

/**
 * Input manager interface
 */
export interface IInputManager extends ISystem {
    /** Register a key handler */
    registerKeyHandler(key: string, handler: InputHandler): void;

    /** Register a mouse handler */
    registerMouseHandler(button: number, handler: InputHandler): void;

    /** Check if key is currently pressed */
    isKeyPressed(key: string): boolean;

    /** Get current mouse position */
    getMousePosition(): Vector2;

    /** Remove all handlers */
    clearHandlers(): void;
}

// ============================================================================
// AUDIO INTERFACES
// ============================================================================

/**
 * Audio manager interface
 */
export interface IAudioManager extends ISystem {
    /** Load an audio asset */
    loadAudio(id: string, url: string): Promise<void>;

    /** Play a sound effect */
    playSFX(id: string, volume?: number): void;

    /** Play background music */
    playMusic(id: string, loop?: boolean, volume?: number): void;

    /** Stop all audio */
    stopAll(): void;

    /** Set master volume */
    setMasterVolume(volume: number): void;
}

// ============================================================================
// ASSET INTERFACES
// ============================================================================

/**
 * Asset loader interface
 */
export interface IAssetLoader {
    /** Load an asset */
    load<T>(url: string, type: AssetType): Promise<T>;

    /** Check if asset is loaded */
    isLoaded(url: string): boolean;

    /** Get loaded asset */
    get<T>(url: string): T | null;

    /** Unload an asset */
    unload(url: string): void;
}

// ============================================================================
// STATE MANAGEMENT INTERFACES
// ============================================================================

/**
 * Game state interface
 */
export interface IGameState {
    /** State name */
    readonly name: string;

    /** Enter this state */
    enter(): void;

    /** Update state logic */
    update(deltaTime: number): void;

    /** Exit this state */
    exit(): void;

    /** Handle input in this state */
    handleInput?(event: InputEvent): void;
}

/**
 * State manager interface
 */
export interface IStateManager extends ISystem {
    /** Add a state */
    addState(state: IGameState): void;

    /** Change to a new state */
    changeState(stateName: string): void;

    /** Push a state onto the stack */
    pushState(stateName: string): void;

    /** Pop the current state */
    popState(): void;

    /** Get current state */
    getCurrentState(): IGameState | null;
}

// ============================================================================
// PHYSICS INTERFACES
// ============================================================================

/**
 * Physics body interface
 */
export interface IPhysicsBody {
    /** Position */
    position: Vector2;

    /** Velocity */
    velocity: Vector2;

    /** Mass */
    mass: number;

    /** Whether body is static */
    isStatic: boolean;

    /** Collision group */
    collisionGroup: number;

    /** Collision mask */
    collisionMask: number;
}

/**
 * Physics system interface
 */
export interface IPhysicsSystem extends ISystem {
    /** Add a physics body */
    addBody(body: IPhysicsBody, entityId: string): void;

    /** Remove a physics body */
    removeBody(entityId: string): void;

    /** Set gravity */
    setGravity(gravity: Vector2): void;

    /** Perform raycast */
    raycast(from: Vector2, to: Vector2): RaycastResult | null;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Asset types supported by the game
 */
export enum AssetType {
    TEXTURE = 'texture',
    AUDIO = 'audio',
    JSON = 'json',
    MODEL = 'model',
    SHADER = 'shader',
}

/**
 * System status for debugging
 */
export interface SystemStatus {
    name: string;
    enabled: boolean;
    entityCount?: number;
    lastUpdateTime?: number;
    averageUpdateTime?: number;
}

/**
 * Renderer performance statistics
 */
export interface RendererStats {
    fps: number;
    frameTime: number;
    drawCalls: number;
    triangles: number;
    memoryUsage?: number;
}

/**
 * Input event data
 */
export interface InputEvent {
    type: 'keydown' | 'keyup' | 'mousedown' | 'mouseup' | 'mousemove';
    key?: string;
    button?: number;
    position?: Vector2;
    deltaPosition?: Vector2;
}

/**
 * Raycast result
 */
export interface RaycastResult {
    hit: boolean;
    distance: number;
    point: Vector2;
    normal: Vector2;
    entity?: IGameEntity;
}

/**
 * Game configuration interface
 */
export interface IGameConfig {
    // Display settings
    width: number;
    height: number;
    targetFPS: number;
    vsync: boolean;

    // Audio settings
    masterVolume: number;
    sfxVolume: number;
    musicVolume: number;

    // Performance settings
    maxParticles: number;
    maxBullets: number;
    cullDistance: number;

    // Gameplay settings
    difficulty: number;
    playerSpeed: number;

    // Debug settings
    showFPS: boolean;
    showHitboxes: boolean;
    debugPhysics: boolean;
}

/**
 * Game statistics interface
 */
export interface IGameStats {
    score: number;
    lives: number;
    level: number;
    time: number;
    bulletsDestroyed: number;
    enemiesDestroyed: number;
    powerUpsCollected: number;
}
