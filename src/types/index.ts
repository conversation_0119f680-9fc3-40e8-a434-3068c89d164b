// Re-export all core interfaces
export * from './CoreInterfaces.types';

// Core game types and interfaces
export interface GameConfig {
    readonly debug: boolean;
    readonly performanceMonitor: boolean;
    readonly targetFPS: number;
    readonly maxBullets: number;
    readonly memoryWarningThreshold: number;
    readonly memoryCriticalThreshold: number;
}

export interface Vector3 {
    readonly x: number;
    readonly y: number;
    readonly z: number;
}

export interface Transform {
    position: Vector3;
    rotation: Vector3;
    scale: Vector3;
}

export interface Entity {
    readonly id: string;
    readonly type: EntityType;
    transform: Transform;
    isActive: boolean;
    components: Map<string, Component>;
}

export interface Component {
    readonly name: string;
    readonly entity: Entity;
    update(deltaTime: number): void;
    destroy(): void;
}

export interface System {
    readonly name: string;
    readonly priority: number;
    initialize(): void;
    update(deltaTime: number): void;
    destroy(): void;
}

export enum EntityType {
    Player = 'player',
    Enemy = 'enemy',
    Bullet = 'bullet',
    PowerUp = 'powerup',
    Obstacle = 'obstacle',
    Particle = 'particle',
}

export enum GameState {
    Loading = 'loading',
    Menu = 'menu',
    Playing = 'playing',
    Paused = 'paused',
    GameOver = 'gameOver',
    Victory = 'victory',
}

export interface GameEvent {
    readonly type: string;
    readonly data?: unknown;
    readonly timestamp: number;
}

export interface AssetManifest {
    readonly textures: Record<string, string>;
    readonly models: Record<string, string>;
    readonly audio: Record<string, string>;
    readonly shaders: Record<string, string>;
}

export interface PerformanceMetrics {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    drawCalls: number;
    triangles: number;
}

// Audio types
export interface AudioConfig {
    readonly masterVolume: number;
    readonly musicVolume: number;
    readonly sfxVolume: number;
    readonly spatialAudio: boolean;
}

// Physics types
export interface PhysicsConfig {
    readonly gravity: Vector3;
    readonly timeStep: number;
    readonly maxSubSteps: number;
    readonly collisionMargin: number;
}

// Graphics types
export interface RenderConfig {
    readonly antialias: boolean;
    readonly shadowsEnabled: boolean;
    readonly postProcessingEnabled: boolean;
    readonly resolution: Vector2;
}

// Input types
export interface InputState {
    readonly keyboard: Record<string, boolean>;
    readonly mouse: {
        readonly position: Vector2;
        readonly buttons: Record<number, boolean>;
    };
    readonly gamepad: {
        readonly connected: boolean;
        readonly buttons: Record<number, boolean>;
        readonly axes: number[];
    };
}
