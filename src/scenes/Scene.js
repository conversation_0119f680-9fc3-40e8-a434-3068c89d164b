import * as THREE from 'three';

export class Scene {
    constructor() {
        this.threeScene = null;
        this.lighting = null;
        this.environment = null;

        // Game object groups
        this.playerGroup = new THREE.Group();
        this.enemyGroup = new THREE.Group();
        this.bulletGroup = new THREE.Group();
        this.effectsGroup = new THREE.Group();
        this.environmentGroup = new THREE.Group();
    }

    async init() {
        this.threeScene = new THREE.Scene();

        // Set up lighting
        this.setupLighting();

        // Set up environment
        this.setupEnvironment();

        // Add groups to scene
        this.threeScene.add(this.playerGroup);
        this.threeScene.add(this.enemyGroup);
        this.threeScene.add(this.bulletGroup);
        this.threeScene.add(this.effectsGroup);
        this.threeScene.add(this.environmentGroup);

        console.log('Scene initialized');
    }

    setupLighting() {
        // Ambient light for general illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.threeScene.add(ambientLight);

        // Main directional light (key light)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;

        // Configure shadow properties
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -20;
        directionalLight.shadow.camera.right = 20;
        directionalLight.shadow.camera.top = 20;
        directionalLight.shadow.camera.bottom = -20;

        this.threeScene.add(directionalLight);

        // Fill light for softer shadows
        const fillLight = new THREE.DirectionalLight(0x6699ff, 0.3);
        fillLight.position.set(-5, 5, -5);
        this.threeScene.add(fillLight);

        // Rim light for edge highlighting
        const rimLight = new THREE.DirectionalLight(0xff9966, 0.2);
        rimLight.position.set(0, 5, -10);
        this.threeScene.add(rimLight);
    }

    setupEnvironment() {
        // Create a simple ground plane
        const groundGeometry = new THREE.PlaneGeometry(50, 50);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x1a1a2e,
            transparent: true,
            opacity: 0.8,
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.environmentGroup.add(ground);

        // Add some atmospheric fog
        this.threeScene.fog = new THREE.Fog(0x0a0a0a, 20, 100);

        // Create a starfield background
        this.createStarField();
    }

    createStarField() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 1000;
        const positions = new Float32Array(starCount * 3);

        for (let i = 0; i < starCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 200; // x
            positions[i + 1] = Math.random() * 50 + 10; // y
            positions[i + 2] = (Math.random() - 0.5) * 200; // z
        }

        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.5,
            transparent: true,
            opacity: 0.8,
        });

        const stars = new THREE.Points(starGeometry, starMaterial);
        this.environmentGroup.add(stars);
    }

    update(deltaTime) {
        // Rotate starfield slowly for dynamic background
        const stars = this.environmentGroup.children.find(child => child instanceof THREE.Points);
        if (stars) {
            stars.rotation.y += deltaTime * 0.05;
        }

        // Update any animated objects in the scene
        this.updateAnimations(deltaTime);
    }

    updateAnimations(_deltaTime) {
        // This will be expanded when animated objects are added
        // For now, just a placeholder
    }

    // Group management methods
    addToPlayerGroup(object) {
        this.playerGroup.add(object);
    }

    addToEnemyGroup(object) {
        this.enemyGroup.add(object);
    }

    addToBulletGroup(object) {
        this.bulletGroup.add(object);
    }

    addToEffectsGroup(object) {
        this.effectsGroup.add(object);
    }

    addToEnvironmentGroup(object) {
        this.environmentGroup.add(object);
    }

    removeFromPlayerGroup(object) {
        this.playerGroup.remove(object);
    }

    removeFromEnemyGroup(object) {
        this.enemyGroup.remove(object);
    }

    removeFromBulletGroup(object) {
        this.bulletGroup.remove(object);
    }

    removeFromEffectsGroup(object) {
        this.effectsGroup.remove(object);
    }

    removeFromEnvironmentGroup(object) {
        this.environmentGroup.remove(object);
    }

    // Clear all dynamic objects (for game reset)
    clearDynamicObjects() {
        // Clear all bullets
        while (this.bulletGroup.children.length > 0) {
            this.bulletGroup.remove(this.bulletGroup.children[0]);
        }

        // Clear all enemies
        while (this.enemyGroup.children.length > 0) {
            this.enemyGroup.remove(this.enemyGroup.children[0]);
        }

        // Clear all effects
        while (this.effectsGroup.children.length > 0) {
            this.effectsGroup.remove(this.effectsGroup.children[0]);
        }
    }

    destroy() {
        if (this.threeScene) {
            // Dispose of all materials and geometries
            this.threeScene.traverse(object => {
                if (object.geometry) {
                    object.geometry.dispose();
                }
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            });

            // Clear the scene
            this.threeScene.clear();
            this.threeScene = null;
        }

        console.log('Scene destroyed');
    }
}
