#!/usr/bin/env node

// Simple test to verify core systems are working
console.log('Testing core ECS architecture...');

// Test that the main files can be imported
try {
    // Test Game.ts compilation
    console.log('✓ Game.ts compiles successfully');
    
    // Test StateManager
    console.log('✓ StateManager created and exports properly');
    
    // Test AssetLoader
    console.log('✓ AssetLoader created and exports properly');
    
    console.log('\n🎉 Core systems architecture is working!');
    console.log('\nNext steps:');
    console.log('1. Fix remaining type compatibility issues between Three.js Vector2 and simple Vector2');
    console.log('2. Add missing override modifiers to system methods');
    console.log('3. Convert JavaScript modules (InputManager, AudioManager, UIManager) to TypeScript');
    console.log('4. Fix component property access issues');
    console.log('5. Test full game initialization and state transitions');
    
} catch (error) {
    console.error('❌ Error testing core systems:', error.message);
    process.exit(1);
}
