# Copilot Instructions

# Playtime Protocol: Innocence Lost

## CRITICAL DEVELOPMENT PHASE RULES

### 🚫 NO CODING WITHOUT EXPLICIT USER INSTRUCTION

- **NEVER write implementation code unless the user explicitly says "code this"
  or "implement this"**
- **NEVE<PERSON> assume the user wants coding when they mention files or show code**
- **NEVER jump ahead to implementation when in preparation phases**
- **ALWAYS stay in the current development phase until explicitly told to
  proceed**

### 🔧 MANDATORY ISSUE RESOLUTION PROTOCOL

- **NO SKIPPING**: Never skip over issues, errors, or problems - fix them
  immediately
- **FIX ALL ISSUES AS THEY ARISE**: When an issue is discovered, it must be
  resolved before proceeding
- **TERMINAL COMMAND APPROVAL**: All terminal commands must be properly approved
  through VS Code settings
- **DESKTOP COMMANDER INTEGRATION**: Ensure Desktop Commander MCP tool has full
  terminal approval and execution rights
- **COMPLETE RESOLUTION**: Fix the root cause, not just symptoms
- **DOCUMENT FIXES**: Record all issue resolutions for future reference

### ✅ WHEN CODING IS EXPLICITLY REQUESTED

When the user explicitly instructs you to code/implement something:

1. **Intelligent Gap Filling**: Automatically identify and complete ALL
   intermediate tasks between the current state and the requested target
2. **Complete Implementation**: Don't just do the specific task - fill in all
   dependencies and prerequisites
3. **Follow Through**: Complete the entire logical sequence of tasks to reach
   the target state
4. **No Half-Measures**: Implement fully functional, production-ready code

## Project Overview

This is a cyberpunk-themed bullet hell game built with modern web technologies.
We are currently in the **PREPARATION PHASE** - no implementation should be done
until preparation is complete.

## Core Development Guidelines

### 1. Planning-First Approach

- **Always check the TODO list** in `.vscode/todo.md` before starting any work
- **Review implementation guidelines** in `.vscode/implementation.md` before
  coding
- **Update documentation** as you work to maintain consistency
- Follow the 4-phase enterprise methodology outlined in the preparation
  templates

### 2. TODO List Management

- **Location**: `.vscode/todo.md`
- **Update Frequency**: After completing each significant task or when new
  requirements emerge
- **Priority System**: Use HIGH/MEDIUM/LOW priority markers
- **Status Tracking**: Use ✅ ❌ 🔄 status indicators
- **Don't over-update**: Only update when there's meaningful progress or changes

### 3. Implementation Consistency

- **Reference**: `.vscode/implementation.md` before starting any coding task
- **Naming Conventions**: Follow established patterns documented in
  implementation.md
- **Architecture Decisions**: Document major decisions for future reference
- **Compatibility**: Ensure new implementations connect properly with existing
  code
- **Update Alongside**: Keep implementation.md current with actual code state

### 4. Code Quality Standards

- **TypeScript First**: Use strict TypeScript for all new code
- **Enterprise Patterns**: Follow enterprise-grade architecture patterns
- **Performance Focus**: Consider performance implications in all decisions
- **Documentation**: Document complex logic and architectural decisions

### 5. File Organization

- Follow the established directory structure
- Keep related files together (components, tests, types)
- Use clear, descriptive file names
- Maintain separation of concerns

### 6. Git Workflow

- Make meaningful commits with clear messages
- Use feature branches for significant changes
- Update documentation in the same commit as code changes
- Follow conventional commit format when possible

### 7. Game Development Specific

- **Assets**: Organize assets by type and usage (textures, models, audio)
- **Shaders**: Keep GLSL files organized and well-commented
- **Performance**: Always consider frame rate and memory usage
- **Cross-browser**: Test on multiple browsers during development

### 8. Communication

- Use clear, technical language in documentation
- Explain architectural decisions and trade-offs
- Document known limitations and future considerations
- Keep stakeholders informed of progress and blockers

## Current Phase: Planning and Preparation

- Do not implement game logic until planning is complete
- Focus on documentation, architecture design, and preparation
- Validate all decisions against game design requirements
- Prepare comprehensive specifications before coding begins

## ✅ PREPARATION PHASE STATUS: COMPLETE

**All foundational tooling and configuration is now working correctly:**

### Fixed Issues (Following "Fix All Issues As They Arise" Protocol):

1. **ESLint Configuration Conflicts RESOLVED** ✅

    - Removed conflicting `.eslintrc.js` and `.eslintrc.json` files
    - Kept working `.eslintrc.cjs` configuration
    - ESLint now properly detects and reports issues in implementation files
    - Used Context7 MCP tools for ESLint best practices research

2. **VS Code Terminal Command Approval CONFIGURED** ✅

    - Added comprehensive MCP tool auto-approval settings
    - Desktop Commander has full terminal execution rights
    - All GitHub Copilot terminal commands pre-approved
    - Added specific MCP terminal execution settings

3. **Development Tooling Verification COMPLETE** ✅
    - Node.js dependencies installed (352 packages) ✅
    - TypeScript compilation working (detects user code issues as expected) ✅
    - Prettier formatting working (formatted 4 JS files) ✅
    - ESLint linting working (detects 73 issues in user implementation files) ✅
    - Vite development server configuration working ✅
    - Security vulnerabilities confirmed as dev-only dependencies ✅

### Ready for Implementation Phase:

- All preparation phase requirements completed
- Configuration files optimized and conflict-free
- Development workflow fully functional
- Issue resolution protocol documented and followed
- MCP tools properly integrated and approved

## Key Files to Reference

- `.vscode/todo.md` - Current task list and priorities
- `.vscode/implementation.md` - Naming conventions and architecture decisions
- `.vscode/game-dev-prompts.md` - Game development specific prompt library
- `.vscode/code-patterns.md` - Proven code patterns and templates
- `docs/GAME_DESIGN_DOCUMENT.md` - Game requirements and specifications
- `docs/TECHNICAL_ARCHITECTURE.md` - System architecture and design patterns
- `PREPARATION_TEMPLATES_GUIDES_METHODOLOGIES/` - Development methodology guides

## MCP Tools Intelligence and Selection

### 9. Intelligent Tool Assessment

Before starting any task, **systematically evaluate all available MCP (Model
Context Protocol) tools** to determine if they can enhance the current work:

#### Tool Categories to Consider

- **Research Tools**: Web search, academic papers, documentation lookup, library
  research
- **Analysis Tools**: Code analysis, performance profiling, security scanning
- **Development Tools**: Code generation, testing, debugging, optimization
- **Project Management**: Task tracking, documentation generation, workflow
  automation
- **Communication Tools**: Team collaboration, reporting, status updates

#### Selection Criteria

1. **Task Relevance**: Does the tool directly support the current objective?
2. **Quality Enhancement**: Will the tool improve the quality or accuracy of the
   output?
3. **Efficiency Gain**: Will the tool significantly reduce time or effort
   required?
4. **Knowledge Gap**: Does the tool provide information not readily available?
5. **Verification Need**: Can the tool help validate assumptions or decisions?

#### Decision Process

1. **Assess Current Task**: Understand the specific requirements and challenges
2. **Inventory Available Tools**: Review all MCP tools that could potentially
   help
3. **Evaluate Fit**: Score each tool against the selection criteria
4. **Select Optimal Set**: Choose the minimum effective set of tools needed
5. **Plan Tool Usage**: Determine the sequence and integration of tool usage

#### Tool Usage Guidelines

- **Start with Research**: Use research tools to gather context before
  implementation
- **Validate with Analysis**: Use analysis tools to verify decisions and
  approaches
- **Enhance with Development**: Use development tools to improve code quality
- **Track with Management**: Use project management tools to maintain progress
- **Document with Communication**: Use communication tools to record decisions

### 10. Prompt Enhancement Protocol

**Every user prompt and internal task assignment must include this suffix:**

```
🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

This directive ensures consistent tool evaluation and selection across all
development activities.

### 11. Prompt Library Integration

**Before starting any coding task:**

- **Reference Game-Dev Prompts**: Check `.vscode/game-dev-prompts.md` for
  task-specific prompt templates
- **Use Code Patterns**: Reference `.vscode/code-patterns.md` for proven
  implementation patterns
- **Follow Prompt Structure**: Use the standardized prompt format for consistent
  results
- **Adapt and Customize**: Modify prompts for specific requirements while
  maintaining structure

**Prompt Library Usage Workflow:**

1. **Identify Task Type**: Determine if task matches a prompt template (ECS,
   Three.js, Performance, etc.)
2. **Select Base Prompt**: Choose the most relevant prompt from
   game-dev-prompts.md
3. **Customize Context**: Fill in placeholders and add project-specific details
4. **Reference Patterns**: Check code-patterns.md for implementation templates
5. **Execute with MCP**: Include the MCP tool directive for enhanced results
6. **Document Learnings**: Update prompt library with improvements and new
   patterns

Remember: Quality planning now saves significant time later. Take time to think
through decisions and document them properly. Always leverage available tools
intelligently to maximize outcome quality.
