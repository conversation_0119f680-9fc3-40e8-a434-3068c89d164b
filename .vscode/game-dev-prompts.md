# Game Development Prompt Library
# Playtime Protocol: Innocence Lost

*Last Updated: Project Setup Phase*

## Usage Instructions
Reference these prompts by name in your requests. Each prompt includes context, requirements, and expected output format.

---

## Entity Component System (ECS) Prompts

### `ECS_COMPONENT_CREATE`
```
Create a TypeScript component for [COMPONENT_NAME] following our ECS architecture:

Requirements:
- Implement IComponent interface from our architecture
- Include proper TypeScript typing with strict mode
- Add JSDoc documentation for public methods
- Follow naming conventions from .vscode/implementation.md
- Include update() method with deltaTime parameter
- Add proper cleanup/destroy method if needed
- Consider performance implications for 60fps target

Context: This is for a cyberpunk bullet hell game using Three.js
Expected: Complete component class with proper interfaces and documentation

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `ECS_SYSTEM_CREATE`
```
Create a TypeScript system for [SYSTEM_NAME] following our ECS architecture:

Requirements:
- Implement ISystem interface with priority property
- Process entities efficiently with component filtering
- Include proper TypeScript typing and JSDoc
- Follow performance guidelines for 60fps target
- Add initialization and cleanup methods
- Consider object pooling where appropriate
- Include error handling for edge cases

Context: Part of bullet hell game engine, will process many entities per frame
Expected: Complete system class with optimized entity processing

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `ECS_ENTITY_CREATE`
```
Create a game entity for [ENTITY_TYPE] (Player/Enemy/Bullet/PowerUp):

Requirements:
- Follow IGameEntity interface structure
- Include relevant components for entity type
- Add proper initialization and cleanup
- Consider object pooling for frequently created entities
- Include collision bounds and interaction logic
- Add state management if needed
- Follow performance optimization patterns

Context: Entity for cyberpunk bullet hell game, needs to work with rendering and physics systems
Expected: Complete entity class with component composition

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Three.js and Graphics Prompts

### `THREEJS_RENDERER_SETUP`
```
Create Three.js renderer setup for [SPECIFIC_PURPOSE]:

Requirements:
- Use WebGL2 with fallback to WebGL1
- Configure for 60fps performance target
- Include proper canvas sizing and aspect ratio handling
- Add performance monitoring hooks
- Include mobile device optimizations
- Set up proper render pipeline for 2D bullet hell game
- Add error handling for WebGL context loss

Context: Main renderer for cyberpunk bullet hell game, needs to handle many dynamic objects
Expected: Complete renderer initialization with performance optimizations

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `SHADER_CREATE`
```
Create GLSL shader for [EFFECT_NAME]:

Requirements:
- Include both vertex and fragment shaders
- Add proper uniform declarations
- Include performance-optimized calculations
- Add comments explaining shader logic
- Consider mobile GPU limitations
- Include fallback for older hardware
- Follow cyberpunk aesthetic guidelines

Context: Visual effect for bullet hell game, needs to run efficiently on various hardware
Expected: Complete vertex/fragment shader pair with documentation

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `TEXTURE_MANAGER_CREATE`
```
Create texture management system for [USAGE_TYPE]:

Requirements:
- Implement texture atlasing for performance
- Add loading state management
- Include memory usage optimization
- Add texture compression support
- Implement proper disposal methods
- Include caching mechanisms
- Add progress tracking for loading

Context: Asset management for bullet hell game with many sprites and effects
Expected: Complete texture manager with optimization features

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Game Logic Prompts

### `COLLISION_SYSTEM_CREATE`
```
Create collision detection system for [COLLISION_TYPE]:

Requirements:
- Implement efficient spatial partitioning (quadtree/grid)
- Support multiple collision shapes (circle, rectangle, polygon)
- Optimize for many bullet-entity checks
- Include collision response handling
- Add debug visualization options
- Consider performance for 60fps with hundreds of objects
- Include proper TypeScript typing

Context: Core system for bullet hell game collision detection
Expected: Optimized collision system with spatial optimization

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `BULLET_PATTERN_CREATE`
```
Create bullet pattern system for [PATTERN_TYPE]:

Requirements:
- Use object pooling for bullet instances
- Include mathematical pattern generation
- Add timing and synchronization controls
- Consider visual appeal and gameplay balance
- Include difficulty scaling parameters
- Add pattern composition capabilities
- Optimize for performance with many bullets

Context: Bullet hell pattern generation system for enemy attacks
Expected: Flexible bullet pattern system with mathematical precision

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `GAME_STATE_MANAGER`
```
Create game state management system for [STATE_TYPES]:

Requirements:
- Implement state machine pattern
- Include state transition validation
- Add save/load capabilities
- Include pause/resume functionality
- Add state-specific update logic
- Include debugging and inspection tools
- Consider performance implications

Context: Core game state management for menu, gameplay, pause, game over states
Expected: Robust state management system with transition controls

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Performance and Optimization Prompts

### `OBJECT_POOL_CREATE`
```
Create object pool for [OBJECT_TYPE]:

Requirements:
- Implement efficient get/release mechanisms
- Add automatic pool size management
- Include performance monitoring hooks
- Add proper object reset/cleanup
- Consider memory allocation patterns
- Include debugging and statistics
- Optimize for frequent allocations

Context: Performance optimization for frequently created game objects
Expected: Efficient object pool with monitoring capabilities

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `PERFORMANCE_MONITOR_CREATE`
```
Create performance monitoring system for [METRICS_TYPE]:

Requirements:
- Track frame rate, memory usage, and render time
- Include real-time performance graphs
- Add performance alerts and warnings
- Include mobile device optimizations
- Add data export capabilities
- Consider minimal performance overhead
- Include profiling integration

Context: Development and production performance monitoring
Expected: Comprehensive performance monitoring with minimal overhead

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Audio System Prompts

### `AUDIO_MANAGER_CREATE`
```
Create audio management system for [AUDIO_TYPE]:

Requirements:
- Use Web Audio API with HTML5 fallback
- Include spatial audio for game effects
- Add audio pooling for frequent sounds
- Include volume and mixing controls
- Add loading and streaming support
- Consider mobile audio limitations
- Include audio compression support

Context: Audio system for cyberpunk bullet hell game with dynamic music and effects
Expected: Professional audio management system with cross-platform support

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Testing Prompts

### `UNIT_TEST_CREATE`
```
Create unit tests for [COMPONENT/SYSTEM_NAME]:

Requirements:
- Use Jest testing framework from our setup
- Include comprehensive test coverage (>80%)
- Add performance benchmarks where relevant
- Include edge case testing
- Add mock objects for dependencies
- Follow AAA pattern (Arrange, Act, Assert)
- Include setup/teardown for game objects

Context: Testing for bullet hell game components with performance requirements
Expected: Complete test suite with performance validation

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `INTEGRATION_TEST_CREATE`
```
Create integration tests for [SYSTEM_INTERACTION]:

Requirements:
- Test system interactions and data flow
- Include performance testing under load
- Add error condition testing
- Include browser compatibility testing
- Add memory leak detection
- Test with realistic game scenarios
- Include automated test reporting

Context: Integration testing for game systems working together
Expected: Comprehensive integration test suite with performance validation

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Debugging and Troubleshooting Prompts

### `DEBUG_SYSTEM_CREATE`
```
Create debugging system for [DEBUG_TARGET]:

Requirements:
- Include visual debugging overlays
- Add performance profiling hooks
- Include state inspection tools
- Add logging with multiple levels
- Include screenshot/recording capabilities
- Add remote debugging support
- Consider production vs development features

Context: Development tools for debugging bullet hell game systems
Expected: Comprehensive debugging toolkit with visual aids

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

### `ERROR_HANDLER_CREATE`
```
Create error handling system for [ERROR_CONTEXT]:

Requirements:
- Include graceful degradation strategies
- Add error reporting and logging
- Include user-friendly error messages
- Add recovery mechanisms where possible
- Include development vs production behavior
- Add error analytics and tracking
- Consider game state preservation

Context: Robust error handling for production bullet hell game
Expected: Professional error handling with recovery capabilities

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Documentation Prompts

### `API_DOCUMENTATION_CREATE`
```
Create API documentation for [MODULE/SYSTEM_NAME]:

Requirements:
- Include comprehensive JSDoc comments
- Add usage examples and code samples
- Include parameter and return type documentation
- Add architecture diagrams where helpful
- Include performance considerations
- Add migration guides for API changes
- Consider multiple skill levels of readers

Context: Developer documentation for bullet hell game systems
Expected: Professional API documentation with examples

🔧 MCP_TOOL_DIRECTIVE: Intelligently assess all available MCP tools for this task. Select and use the optimal combination of tools that will enhance quality, accuracy, and efficiency. Justify tool selection based on task requirements and expected value delivery. Consider research, analysis, development, management, and communication tool categories.
```

---

## Prompt Usage Guidelines

### How to Use These Prompts:
1. **Select Appropriate Prompt**: Choose the prompt that matches your task
2. **Fill in Placeholders**: Replace [PLACEHOLDER] with specific details
3. **Add Context**: Include any additional project-specific context
4. **Review Requirements**: Ensure all requirements align with your needs
5. **Execute Request**: Use the complete prompt for consistent results

### Customization Notes:
- All prompts include the MCP tool directive for intelligent tool usage
- Requirements are based on our enterprise-grade standards
- Context includes cyberpunk bullet hell game specifics
- Expected outputs align with our architecture decisions

### Prompt Evolution:
- Update prompts as patterns emerge
- Add new prompts for recurring tasks
- Refine requirements based on results
- Keep aligned with implementation.md guidelines
