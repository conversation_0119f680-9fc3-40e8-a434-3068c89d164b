# Implementation Guidelines
# Playtime Protocol: Innocence Lost

*Last Updated: Initial Setup*

## Naming Conventions

### Files and Directories
- **Components**: PascalCase (e.g., `GameRenderer.ts`, `PlayerController.ts`)
- **Utilities**: camelCase (e.g., `mathUtils.ts`, `collisionDetection.ts`)
- **Types**: PascalCase with `.types.ts` suffix (e.g., `GameEntity.types.ts`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `GAME_CONSTANTS.ts`)
- **Shaders**: kebab-case with extension (e.g., `particle-vertex.glsl`, `bloom-fragment.glsl`)
- **Assets**: kebab-case grouping (e.g., `player-sprite.png`, `enemy-bullet-sound.wav`)

### Code Elements
- **Classes**: PascalCase (e.g., `GameEngine`, `BulletPool`)
- **Interfaces**: PascalCase with `I` prefix (e.g., `IGameEntity`, `IRenderComponent`)
- **Types**: PascalCase with `T` suffix (e.g., `GameStateT`, `PositionT`)
- **Enums**: PascalCase (e.g., `EntityType`, `GamePhase`)
- **Functions/Methods**: camelCase (e.g., `updateGameState`, `renderFrame`)
- **Variables**: camelCase (e.g., `playerPosition`, `bulletSpeed`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_BULLETS`, `SCREEN_WIDTH`)

## Directory Structure Standards

### Source Code Organization
```
src/
├── components/          # Reusable game components
│   ├── entities/       # Game entities (Player, Enemy, Bullet)
│   ├── systems/        # ECS systems (Render, Physics, Input)
│   └── ui/            # User interface components
├── core/               # Core game engine functionality
│   ├── engine/        # Main game engine
│   ├── managers/      # Resource and state managers
│   └── utils/         # Utility functions
├── shaders/           # GLSL shader files
│   ├── vertex/       # Vertex shaders
│   └── fragment/     # Fragment shaders
├── types/             # TypeScript type definitions
├── constants/         # Game constants and configuration
└── assets/           # Asset loading and management
```

### Asset Organization
```
public/assets/
├── textures/          # All image assets
│   ├── sprites/      # Character and object sprites
│   ├── ui/           # User interface elements
│   └── effects/      # Visual effects and particles
├── audio/            # All sound assets
│   ├── sfx/         # Sound effects
│   ├── music/       # Background music
│   └── voice/       # Voice acting (if any)
├── models/           # 3D models (if used)
└── fonts/            # Custom fonts
```

## Architecture Decisions

### Core Patterns
1. **Entity Component System (ECS)**: All game objects follow ECS pattern
2. **Observer Pattern**: For event handling and game state changes
3. **Object Pooling**: For bullets, particles, and frequently created/destroyed objects
4. **State Machine**: For game states (menu, playing, paused, game over)
5. **Module Pattern**: Each major system is encapsulated in its own module

### Technology Stack Decisions
- **Rendering**: Three.js for WebGL abstraction
- **Physics**: Custom 2D physics with Three.js math utilities
- **Audio**: Web Audio API with fallback to HTML5 audio
- **Input**: Native event handling with custom input manager
- **Build System**: Vite for development and production builds
- **Type System**: Strict TypeScript with comprehensive type definitions

### Performance Strategies
1. **Frame Rate Priority**: Target 60fps on mid-range hardware
2. **Memory Management**: Explicit object pooling for high-frequency objects
3. **Render Optimization**: Frustum culling, batched drawing, texture atlasing
4. **Asset Loading**: Progressive loading with smart preloading
5. **Code Splitting**: Lazy loading for non-critical game features

## Component Architecture

### Game Entity Structure
```typescript
interface IGameEntity {
  id: string;
  position: Vector2;
  rotation: number;
  scale: Vector2;
  active: boolean;
  components: Map<string, IComponent>;
}
```

### Component Interface
```typescript
interface IComponent {
  entity: IGameEntity;
  update(deltaTime: number): void;
  render?(renderer: IRenderer): void;
  destroy?(): void;
}
```

### System Interface
```typescript
interface ISystem {
  priority: number;
  update(entities: IGameEntity[], deltaTime: number): void;
  initialize?(): void;
  cleanup?(): void;
}
```

## Coding Standards

### TypeScript Guidelines
- **Strict Mode**: Always use strict TypeScript settings
- **Explicit Types**: Avoid `any`, prefer specific types or `unknown`
- **Interface Segregation**: Keep interfaces focused and cohesive
- **Null Safety**: Use optional chaining and nullish coalescing
- **Error Handling**: Prefer result types over exceptions for expected errors

### Code Style Rules
- **Indentation**: 2 spaces (configured in Prettier)
- **Line Length**: 100 characters maximum
- **Semicolons**: Always use semicolons
- **Quotes**: Single quotes for strings, double quotes for JSX attributes
- **Trailing Commas**: Always use trailing commas in multi-line structures

### Documentation Requirements
- **Public APIs**: JSDoc comments for all public methods and classes
- **Complex Logic**: Inline comments explaining non-obvious code
- **Architecture Decisions**: Document in this file or separate ADR files
- **TODO Comments**: Use TODO with assignee and ticket reference

## Testing Strategy

### Test Organization
```
tests/
├── unit/              # Unit tests for individual components
├── integration/       # Integration tests for system interactions
├── performance/       # Performance and load testing
└── fixtures/          # Test data and mock objects
```

### Testing Guidelines
- **Test Coverage**: Aim for 80%+ coverage on business logic
- **Naming**: `describe` blocks use PascalCase, `it` blocks use sentences
- **Mocking**: Mock external dependencies, test real implementations
- **Performance Tests**: Include tests for frame rate and memory usage

## Asset Management

### Asset Loading Strategy
1. **Critical Path**: Load essential assets first (player, basic UI)
2. **Progressive Loading**: Load additional assets during gameplay
3. **Lazy Loading**: Load level-specific assets when needed
4. **Caching**: Implement proper cache headers and local storage

### Asset Optimization
- **Textures**: Use texture atlases, compress with appropriate formats
- **Audio**: Use compressed formats (OGG, MP3) with quality balance
- **Models**: Optimize polygon count and texture resolution
- **Fonts**: Use web fonts with proper fallbacks

## Error Handling

### Error Categories
1. **Fatal Errors**: Crash the game gracefully with error reporting
2. **Recoverable Errors**: Log and continue with degraded functionality
3. **Expected Errors**: Handle silently or with user feedback
4. **Development Errors**: Throw immediately in development mode

### Logging Strategy
- **Levels**: ERROR, WARN, INFO, DEBUG
- **Production**: Only ERROR and WARN levels
- **Development**: All levels with detailed stack traces
- **Performance**: Use console.time for performance debugging

## Browser Compatibility

### Target Browsers
- **Primary**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Secondary**: Older versions with graceful degradation
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+

### Feature Detection
- **WebGL**: Check for WebGL2 support, fallback to WebGL1
- **Audio**: Check Web Audio API support, fallback to HTML5 audio
- **Input**: Check for modern event handling, provide polyfills

## Version Control

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

### Commit Types
- **feat**: New features
- **fix**: Bug fixes
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **perf**: Performance improvements
- **test**: Test additions or modifications
- **chore**: Build process or auxiliary tool changes

### Branch Strategy
- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Individual feature branches
- **hotfix/**: Critical production fixes

## Development Workflow

### Daily Development Process
1. **Check TODO**: Review `.vscode/todo.md` for current priorities
2. **Update Dependencies**: Check for and apply security updates
3. **Run Tests**: Ensure all tests pass before starting work
4. **Code Review**: Review changes before committing
5. **Update Documentation**: Keep documentation current with code changes

### Release Process
1. **Feature Complete**: All planned features implemented and tested
2. **Performance Review**: Meet all performance requirements
3. **Cross-browser Testing**: Verify compatibility across target browsers
4. **Documentation Update**: Ensure all documentation is current
5. **Version Tagging**: Create semantic version tags

---

## Future Considerations

### Scalability Preparations
- **Modular Loading**: Design for potential code splitting
- **Asset Streaming**: Prepare for large asset libraries
- **Performance Monitoring**: Plan for production performance tracking
- **Internationalization**: Structure for potential multi-language support

### Technology Evolution
- **WebGPU Migration**: Monitor WebGPU adoption for future upgrade
- **Framework Updates**: Stay current with Three.js and TypeScript versions
- **Tool Evolution**: Evaluate new development tools as they mature

---

*This document should be updated as architectural decisions are made and patterns are established. Keep it synchronized with actual code implementation.*
