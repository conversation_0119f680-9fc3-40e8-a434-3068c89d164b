# Code Patterns Library
# Playtime Protocol: Innocence Lost

*Last Updated: Project Setup Phase*

## Usage Instructions
These are proven code patterns and templates for common game development tasks. Reference by pattern name and adapt to specific needs.

---

## Entity Component System Patterns

### Component Base Pattern
```typescript
// Pattern: COMPONENT_BASE
// Usage: Foundation for all game components

interface IComponent {
  entity: IGameEntity;
  enabled: boolean;
  update(deltaTime: number): void;
  destroy?(): void;
}

abstract class BaseComponent implements IComponent {
  public entity: IGameEntity;
  public enabled: boolean = true;

  constructor(entity: IGameEntity) {
    this.entity = entity;
  }

  abstract update(deltaTime: number): void;

  public destroy(): void {
    // Default cleanup
    this.enabled = false;
  }
}

// Implementation Template:
export class [ComponentName]Component extends BaseComponent {
  // Component-specific properties
  
  constructor(entity: IGameEntity, /* specific parameters */) {
    super(entity);
    // Initialize component-specific properties
  }

  public update(deltaTime: number): void {
    if (!this.enabled) return;
    
    // Component update logic
  }

  public destroy(): void {
    // Component-specific cleanup
    super.destroy();
  }
}
```

### System Base Pattern
```typescript
// Pattern: SYSTEM_BASE
// Usage: Foundation for all game systems

interface ISystem {
  priority: number;
  enabled: boolean;
  update(entities: IGameEntity[], deltaTime: number): void;
  initialize?(): void;
  cleanup?(): void;
}

abstract class BaseSystem implements ISystem {
  public readonly priority: number;
  public enabled: boolean = true;
  protected requiredComponents: string[] = [];

  constructor(priority: number) {
    this.priority = priority;
  }

  protected filterEntities(entities: IGameEntity[]): IGameEntity[] {
    return entities.filter(entity => {
      return this.requiredComponents.every(componentType =>
        entity.components.has(componentType)
      );
    });
  }

  abstract update(entities: IGameEntity[], deltaTime: number): void;
}

// Implementation Template:
export class [SystemName]System extends BaseSystem {
  constructor() {
    super([PRIORITY_NUMBER]); // 0-100, lower = higher priority
    this.requiredComponents = ['[ComponentType1]', '[ComponentType2]'];
  }

  public update(entities: IGameEntity[], deltaTime: number): void {
    if (!this.enabled) return;

    const validEntities = this.filterEntities(entities);
    
    for (const entity of validEntities) {
      // Process each entity
    }
  }
}
```

### Entity Factory Pattern
```typescript
// Pattern: ENTITY_FACTORY
// Usage: Consistent entity creation with proper component setup

export class EntityFactory {
  private static idCounter: number = 0;

  public static create[EntityType](/* parameters */): IGameEntity {
    const entity: IGameEntity = {
      id: `${EntityType.toLowerCase()}_${++this.idCounter}`,
      position: new Vector2(/* x, y */),
      rotation: 0,
      scale: new Vector2(1, 1),
      active: true,
      components: new Map<string, IComponent>()
    };

    // Add required components
    entity.components.set('[ComponentType1]', new [ComponentType1]Component(entity, /* params */));
    entity.components.set('[ComponentType2]', new [ComponentType2]Component(entity, /* params */));

    return entity;
  }
}

// Usage Example:
const player = EntityFactory.createPlayer(startPosition, playerConfig);
const enemy = EntityFactory.createEnemy(spawnPosition, enemyType);
```

---

## Object Pooling Patterns

### Generic Pool Pattern
```typescript
// Pattern: OBJECT_POOL
// Usage: Efficient object reuse for frequently created/destroyed objects

export class ObjectPool<T> {
  private available: T[] = [];
  private inUse: Set<T> = new Set();
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  private maxSize: number;

  constructor(
    createFunction: () => T,
    resetFunction: (obj: T) => void,
    initialSize: number = 10,
    maxSize: number = 100
  ) {
    this.createFn = createFunction;
    this.resetFn = resetFunction;
    this.maxSize = maxSize;

    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.available.push(this.createFn());
    }
  }

  public get(): T {
    let obj: T;

    if (this.available.length > 0) {
      obj = this.available.pop()!;
    } else {
      obj = this.createFn();
    }

    this.inUse.add(obj);
    return obj;
  }

  public release(obj: T): void {
    if (this.inUse.has(obj)) {
      this.inUse.delete(obj);
      this.resetFn(obj);

      if (this.available.length < this.maxSize) {
        this.available.push(obj);
      }
    }
  }

  public getStats(): { available: number; inUse: number; total: number } {
    return {
      available: this.available.length,
      inUse: this.inUse.size,
      total: this.available.length + this.inUse.size
    };
  }
}

// Usage Template:
const bulletPool = new ObjectPool<Bullet>(
  () => new Bullet(),
  (bullet) => bullet.reset(),
  50, // initial size
  200 // max size
);
```

---

## Performance Monitoring Patterns

### Performance Monitor Pattern
```typescript
// Pattern: PERFORMANCE_MONITOR
// Usage: Real-time performance tracking and optimization detection

export class PerformanceMonitor {
  private frameTime: number = 0;
  private frameTimes: number[] = [];
  private lastFrameTime: number = 0;
  private fpsHistory: number[] = [];
  private memoryUsage: number[] = [];

  public startFrame(): void {
    this.lastFrameTime = performance.now();
  }

  public endFrame(): void {
    const currentTime = performance.now();
    this.frameTime = currentTime - this.lastFrameTime;
    
    this.frameTimes.push(this.frameTime);
    if (this.frameTimes.length > 60) {
      this.frameTimes.shift();
    }

    const fps = 1000 / this.frameTime;
    this.fpsHistory.push(fps);
    if (this.fpsHistory.length > 300) { // 5 seconds at 60fps
      this.fpsHistory.shift();
    }

    // Memory tracking (if available)
    if ((performance as any).memory) {
      const memory = (performance as any).memory.usedJSHeapSize;
      this.memoryUsage.push(memory);
      if (this.memoryUsage.length > 60) {
        this.memoryUsage.shift();
      }
    }
  }

  public getAverageFPS(): number {
    if (this.fpsHistory.length === 0) return 0;
    return this.fpsHistory.reduce((a, b) => a + b) / this.fpsHistory.length;
  }

  public getMinFPS(): number {
    return this.fpsHistory.length > 0 ? Math.min(...this.fpsHistory) : 0;
  }

  public getMaxFrameTime(): number {
    return this.frameTimes.length > 0 ? Math.max(...this.frameTimes) : 0;
  }

  public isPerformanceGood(): boolean {
    const avgFPS = this.getAverageFPS();
    const minFPS = this.getMinFPS();
    return avgFPS >= 55 && minFPS >= 45; // Performance thresholds
  }
}

// Usage in game loop:
const perfMonitor = new PerformanceMonitor();

function gameLoop(): void {
  perfMonitor.startFrame();
  
  // Game logic here
  
  perfMonitor.endFrame();
  
  if (!perfMonitor.isPerformanceGood()) {
    console.warn('Performance degradation detected');
  }
}
```

---

## State Management Patterns

### State Machine Pattern
```typescript
// Pattern: STATE_MACHINE
// Usage: Clean state management for game states

interface IState {
  name: string;
  enter(): void;
  update(deltaTime: number): void;
  exit(): void;
}

export class StateMachine {
  private states: Map<string, IState> = new Map();
  private currentState: IState | null = null;
  private previousState: IState | null = null;

  public addState(state: IState): void {
    this.states.set(state.name, state);
  }

  public changeState(stateName: string): boolean {
    const newState = this.states.get(stateName);
    if (!newState) {
      console.error(`State '${stateName}' not found`);
      return false;
    }

    if (this.currentState) {
      this.currentState.exit();
      this.previousState = this.currentState;
    }

    this.currentState = newState;
    this.currentState.enter();
    return true;
  }

  public update(deltaTime: number): void {
    if (this.currentState) {
      this.currentState.update(deltaTime);
    }
  }

  public getCurrentStateName(): string | null {
    return this.currentState ? this.currentState.name : null;
  }

  public getPreviousStateName(): string | null {
    return this.previousState ? this.previousState.name : null;
  }
}

// State Implementation Template:
export class [StateName]State implements IState {
  public readonly name = '[StateName]';

  public enter(): void {
    // State initialization
  }

  public update(deltaTime: number): void {
    // State update logic
  }

  public exit(): void {
    // State cleanup
  }
}
```

---

## Event System Patterns

### Event Emitter Pattern
```typescript
// Pattern: EVENT_EMITTER
// Usage: Decoupled communication between game systems

type EventListener = (...args: any[]) => void;

export class EventEmitter {
  private listeners: Map<string, EventListener[]> = new Map();

  public on(event: string, listener: EventListener): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  public off(event: string, listener: EventListener): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  public emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }

  public once(event: string, listener: EventListener): void {
    const onceListener = (...args: any[]) => {
      listener(...args);
      this.off(event, onceListener);
    };
    this.on(event, onceListener);
  }

  public removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}

// Global event bus pattern:
export const GameEvents = new EventEmitter();

// Usage examples:
GameEvents.on('player:hit', (damage: number) => { /* handle */ });
GameEvents.on('enemy:destroyed', (enemy: Enemy, score: number) => { /* handle */ });
GameEvents.emit('bullet:fired', bulletData);
```

---

## Asset Loading Patterns

### Asset Manager Pattern
```typescript
// Pattern: ASSET_MANAGER
// Usage: Centralized asset loading and caching

interface AssetConfig {
  id: string;
  url: string;
  type: 'texture' | 'audio' | 'json' | 'model';
}

export class AssetManager {
  private assets: Map<string, any> = new Map();
  private loading: Map<string, Promise<any>> = new Map();
  private loadProgress: Map<string, number> = new Map();

  public async loadAsset(config: AssetConfig): Promise<any> {
    // Check if already loaded
    if (this.assets.has(config.id)) {
      return this.assets.get(config.id);
    }

    // Check if currently loading
    if (this.loading.has(config.id)) {
      return this.loading.get(config.id);
    }

    // Start loading
    const loadPromise = this.loadAssetByType(config);
    this.loading.set(config.id, loadPromise);

    try {
      const asset = await loadPromise;
      this.assets.set(config.id, asset);
      this.loading.delete(config.id);
      this.loadProgress.set(config.id, 100);
      return asset;
    } catch (error) {
      this.loading.delete(config.id);
      throw error;
    }
  }

  private async loadAssetByType(config: AssetConfig): Promise<any> {
    switch (config.type) {
      case 'texture':
        return this.loadTexture(config.url);
      case 'audio':
        return this.loadAudio(config.url);
      case 'json':
        return this.loadJSON(config.url);
      default:
        throw new Error(`Unsupported asset type: ${config.type}`);
    }
  }

  private async loadTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      const loader = new THREE.TextureLoader();
      loader.load(url, resolve, undefined, reject);
    });
  }

  private async loadAudio(url: string): Promise<AudioBuffer> {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    const audioContext = new AudioContext();
    return audioContext.decodeAudioData(arrayBuffer);
  }

  private async loadJSON(url: string): Promise<any> {
    const response = await fetch(url);
    return response.json();
  }

  public getAsset<T>(id: string): T | null {
    return this.assets.get(id) || null;
  }

  public isLoaded(id: string): boolean {
    return this.assets.has(id);
  }

  public getLoadProgress(id: string): number {
    return this.loadProgress.get(id) || 0;
  }
}

// Usage:
const assetManager = new AssetManager();
const playerTexture = await assetManager.loadAsset({
  id: 'player_sprite',
  url: '/assets/textures/player.png',
  type: 'texture'
});
```

---

## Error Handling Patterns

### Result Type Pattern
```typescript
// Pattern: RESULT_TYPE
// Usage: Type-safe error handling without exceptions

type Result<T, E = Error> = {
  success: true;
  data: T;
} | {
  success: false;
  error: E;
};

export function createSuccess<T>(data: T): Result<T> {
  return { success: true, data };
}

export function createError<E>(error: E): Result<never, E> {
  return { success: false, error };
}

// Usage in game systems:
export function loadGameData(path: string): Result<GameData, string> {
  try {
    const data = JSON.parse(localStorage.getItem(path) || '');
    return createSuccess(data);
  } catch (error) {
    return createError(`Failed to load game data: ${error}`);
  }
}

// Safe usage:
const result = loadGameData('savegame');
if (result.success) {
  console.log('Loaded data:', result.data);
} else {
  console.error('Load failed:', result.error);
}
```

### Try-Catch Wrapper Pattern
```typescript
// Pattern: SAFE_OPERATION
// Usage: Consistent error handling for risky operations

export async function safeOperation<T>(
  operation: () => Promise<T>,
  fallback?: T,
  onError?: (error: Error) => void
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    if (onError) {
      onError(error instanceof Error ? error : new Error(String(error)));
    }
    return fallback ?? null;
  }
}

// Usage:
const texture = await safeOperation(
  () => assetManager.loadAsset(textureConfig),
  defaultTexture,
  (error) => console.error('Texture load failed:', error)
);
```

---

## Pattern Usage Guidelines

### When to Use Each Pattern:
- **Component/System Base**: Foundation for all ECS architecture
- **Entity Factory**: Consistent entity creation and configuration
- **Object Pool**: High-frequency object creation (bullets, particles, effects)
- **Performance Monitor**: Development and production performance tracking
- **State Machine**: Game state management (menu, gameplay, pause)
- **Event Emitter**: Decoupled system communication
- **Asset Manager**: Centralized resource loading and caching
- **Result Type**: Type-safe error handling in critical systems
- **Safe Operation**: Wrapping risky operations with fallbacks

### Customization Guidelines:
1. **Adapt to Context**: Modify patterns for specific game requirements
2. **Maintain Types**: Keep strict TypeScript typing throughout
3. **Add Documentation**: Include JSDoc for complex patterns
4. **Performance First**: Consider 60fps impact for all patterns
5. **Test Coverage**: Create tests for pattern implementations
6. **Error Handling**: Include proper error handling in all patterns

### Pattern Evolution:
- Refine patterns based on actual usage
- Add new patterns as needs emerge
- Remove or deprecate unused patterns
- Keep aligned with implementation.md standards
- Document pattern relationships and dependencies
