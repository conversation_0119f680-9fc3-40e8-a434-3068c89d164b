module.exports = {
    root: true,
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module'
        // project: './tsconfig.json'
    },
    plugins: [
        '@typescript-eslint'
    ],
    extends: [
        'eslint:recommended'
        // '@typescript-eslint/recommended'
        // '@typescript-eslint/recommended-requiring-type-checking'
    ],
    env: {
        browser: true,
        es2022: true,
        node: true
    },
    rules: {
        // TypeScript specific rules (basic ones without requiring type checking)
        '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
        '@typescript-eslint/no-explicit-any': 'warn',
        // '@typescript-eslint/prefer-const': 'error', // Removed due to config issue
        '@typescript-eslint/no-inferrable-types': 'off',

        // Game development specific rules
        'no-console': ['warn', { allow: ['warn', 'error'] }],
        'prefer-const': 'error',
        'no-var': 'error',
        'eqeqeq': 'error',
        'curly': 'error',

        // Code style
        'indent': ['error', 4],
        'quotes': ['error', 'single'],
        'semi': ['error', 'always'],
        'comma-trailing': 'off',

        // Performance considerations for games
        'no-loop-func': 'error',
        'no-eval': 'error',
        'no-implied-eval': 'error',

        // Three.js and WebGL considerations
        'no-undef': 'error'
    },
    ignorePatterns: [
        'dist/**',
        'node_modules/**',
        'public/**',
        '*.config.js',
        '*.config.ts'
    ]
};
