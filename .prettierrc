{"semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "es5", "printWidth": 120, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "embeddedLanguageFormatting": "auto", "overrides": [{"files": ["*.glsl", "*.vert", "*.frag"], "options": {"parser": "glsl-parser"}}, {"files": ["*.md"], "options": {"printWidth": 80, "proseWrap": "always"}}]}