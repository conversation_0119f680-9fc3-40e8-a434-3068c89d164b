#!/bin/bash
# Development Environment Setup Script
# Run this script to set up the complete development environment

set -e

echo "🎮 Setting up Playtime Protocol: Innocence Lost Development Environment"
echo "======================================================================"

# Check Node.js version
echo "📋 Checking Node.js version..."
node_version=$(node --version | cut -d'v' -f2)
required_version="18.0.0"

if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✅ Node.js version $node_version is compatible"
else
    echo "❌ Node.js version $node_version is too old. Please upgrade to $required_version or higher"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Setup git hooks
echo "🔧 Setting up git hooks..."
if [ -d ".git" ]; then
    npm run prepare 2>/dev/null || echo "📝 Note: husky not configured yet"
else
    echo "📝 Note: Not a git repository. Initialize git first for hooks."
fi

# Create necessary directories
echo "📁 Creating directory structure..."
mkdir -p public/assets/{audio/music,audio/sfx,models,textures}
mkdir -p src/{shaders,test,tools}
mkdir -p docs/assets
mkdir -p tests/{unit,integration,e2e}

# Set up environment file
echo "⚙️ Setting up environment configuration..."
if [ ! -f ".env" ]; then
    cat > .env << EOL
# Development Environment Variables
NODE_ENV=development
VITE_GAME_DEBUG=true
VITE_GAME_VERSION=1.0.0-dev
VITE_ENABLE_STATS=true
VITE_ENABLE_DEBUG_UI=true
EOL
    echo "📝 Created .env file with development defaults"
else
    echo "📝 .env file already exists"
fi

# Setup IDE configuration
echo "🛠️ Configuring IDE settings..."
if [ ! -f ".vscode/extensions.json" ]; then
    cat > .vscode/extensions.json << EOL
{
    "recommendations": [
        "esbenp.prettier-vscode",
        "@typescript-eslint/typescript-eslint",
        "ms-vscode.vscode-typescript-next",
        "slevesque.shader",
        "raczzalan.webgl-glsl-editor",
        "ms-vscode.js-debug",
        "ZixuanChen.vitest-explorer"
    ]
}
EOL
    echo "📝 Created VS Code extensions recommendations"
fi

# Run initial build check
echo "🔨 Running initial build check..."
npm run type-check

# Run linting
echo "📏 Running code quality checks..."
npm run lint

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Start development server: npm run dev"
echo "2. Run tests: npm test"
echo "3. Open http://localhost:3000 in your browser"
echo ""
echo "Happy coding! 🚀"
