# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
.vite/
.cache/

# Environment files
.env
.env.local
.env.*.local

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Game-specific temporary files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# Asset processing temporary files
assets_temp/
processed_assets/
texture_cache/
model_cache/
audio_cache/

# Performance and profiling
coverage/
.nyc_output/
*.lcov
perf/
profiling/

# Backup files
*.backup
*.bak
*.orig

# Lock files (choose one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Test artifacts
test-results/
playwright-report/
test-results.xml

# Documentation build
docs/.vitepress/cache
docs/.vitepress/dist
