import { defineConfig } from 'vite'
import glsl from 'vite-plugin-glsl'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    glsl({
      // Process all shader files
      include: ['**/*.glsl', '**/*.vert', '**/*.frag'],
      exclude: 'node_modules/**',
      warnDuplicatedImports: true,
      defaultExtension: 'glsl',
      compress: false,
      watch: true
    })
  ],

  root: '.',
  publicDir: 'public',

  // Path resolution for enterprise-grade imports
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/core': resolve(__dirname, 'src/core'),
      '@/game': resolve(__dirname, 'src/game'),
      '@/graphics': resolve(__dirname, 'src/graphics'),
      '@/systems': resolve(__dirname, 'src/systems'),
      '@/ui': resolve(__dirname, 'src/ui'),
      '@/audio': resolve(__dirname, 'src/audio'),
      '@/physics': resolve(__dirname, 'src/physics'),
      '@/ai': resolve(__dirname, 'src/ai'),
      '@/data': resolve(__dirname, 'src/data'),
      '@/shaders': resolve(__dirname, 'src/shaders'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/test': resolve(__dirname, 'src/test')
    }
  },

  // Development server configuration
  server: {
    port: 3000,
    host: true,
    cors: true,
    hmr: {
      overlay: true
    }
  },

  // Build optimization for game assets
  build: {
    target: 'es2022',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      input: {
        main: './index.html'
      },
      output: {
        manualChunks: {
          'three': ['three'],
          'physics': ['cannon-es'],
          'vendor': ['stats.js', 'lil-gui']
        }
      }
    },
    // Game-specific optimizations
    chunkSizeWarningLimit: 1000,
    cssCodeSplit: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // Asset optimization
  assetsInclude: ['**/*.glb', '**/*.gltf', '**/*.fbx', '**/*.obj'],

  // Environment variables
  envPrefix: 'GAME_',

  // Performance optimizations
  optimizeDeps: {
    include: ['three', 'cannon-es', 'stats.js', 'lil-gui'],
    exclude: ['@types/three']
  }
})
