{"name": "playtime-protocol-innocence-lost", "version": "1.0.0", "description": "A top-down shooter moderate bullet hell game with 2.5D cartoonish graphics - Enterprise-grade implementation", "main": "src/main.js", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "lint": "eslint src --ext .ts,.js --fix", "format": "prettier --write src/**/*.{ts,js,css,html}", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"cannon-es": "^0.20.0", "lil-gui": "^0.19.0", "stats.js": "^0.17.0", "three": "^0.160.0"}, "devDependencies": {"@types/stats.js": "^0.17.0", "@types/three": "^0.160.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitest/coverage-v8": "^1.1.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-glsl": "^1.1.2", "vitest": "^1.1.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["game", "bullet-hell", "2.5d", "planning-phase"], "author": "Playtime Protocol Team", "license": "MIT"}