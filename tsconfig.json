{
  "compilerOptions": {
    "target": "ES2022",
    "lib": [
      "ES2022",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    /* Enterprise-grade strict type checking */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    /* Path mapping for organized imports */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@/core/*": [
        "src/core/*"
      ],
      "@/game/*": [
        "src/game/*"
      ],
      "@/graphics/*": [
        "src/graphics/*"
      ],
      "@/systems/*": [
        "src/systems/*"
      ],
      "@/ui/*": [
        "src/ui/*"
      ],
      "@/audio/*": [
        "src/audio/*"
      ],
      "@/physics/*": [
        "src/physics/*"
      ],
      "@/ai/*": [
        "src/ai/*"
      ],
      "@/data/*": [
        "src/data/*"
      ],
      "@/shaders/*": [
        "src/shaders/*"
      ],
      "@/utils/*": [
        "src/utils/*"
      ],
      "@/types/*": [
        "src/types/*"
      ],
      "@/test/*": [
        "src/test/*"
      ]
    },
    /* Advanced options */
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.vue",
    "vite.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}